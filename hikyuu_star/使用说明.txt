以下均在 from hikyuu.interactive import * 后完成。
如没有特殊说明，策略库没有变化的情况下，通常只需要执行一次。

添加远程策略库：
add_remote_hub("star", "https://gitcode.com/hikyuu/hikyuu_star.git", "main")

添加本地仓库，用于自己的策略部件策略库，修改调整：
1. 有部分 part 可能使用到公共的策略部件库，建议先更新公共的策略库：update_hub("default")
2. 另起一个名字，如 star-dev, 添加本地 star_hub 仓库：add_local_hub("star-dev", r"D:\workspace\hikyuu_star\star_hub")
   后一参数为本仓库的本地地址，请自行修改。
3. 在添加完本地 star_hub 后，后续更新，只需要执行 update_hub("star") 即可。（前提本地路径无变化）