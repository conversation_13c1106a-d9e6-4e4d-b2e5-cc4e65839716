%matplotlib inline
%time from hikyuu.interactive import *

# 定义回测时间
start_date = Datetime(20200101)
end_date = Datetime(20240501)
query = Query(start_date, end_date)

# 指定分析对象
stk = sm['sh510050']
print(stk)
k = stk.get_kdata(Query(start_date, end_date))

my_sys = get_part("hikyuu_star.sys.emv择时")
# my_sys = get_part("start.sys.趋势布林带")
my_sys = get_part("hikyuu_star.sys.趋势双均线")


# 定义回测账户，并指定成本算法
my_tm = crtTM(start_date, init_cash=100000, cost_func=TC_FixedA2017())

for name in ('趋势双均线', '趋势布林带', 'emv择时'):
    my_sys = get_part(f"hikyuu_star.sys.{name}")
    my_sys.tm = my_tm
    my_sys.run(stk, query)
    my_sys.performance()

df['已平仓帐户收益率%'].hist()