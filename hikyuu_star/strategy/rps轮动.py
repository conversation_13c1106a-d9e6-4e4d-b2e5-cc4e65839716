

import matplotlib.pylab as plt
from hikyuu.interactive import *
import os
import sys
if sys.platform == 'win32':
    os.system('chcp 65001')

rps = get_part("star.ind.rps")

stk = sm['sz000001']
start_date = Datetime(20100101)
end_date = Datetime(20250601)
test_query = Query(start_date, end_date)

# 注：直接使用沪深300当前成分股本质是一种未来函数
# stks = get_part('default.other.stks-沪深300')
stks = [s for s in blocka if s.valid]
# stks = [sm['sz000001'], sm['sz000002']]
print('股票数: ', len(stks))
mf = MF_EqualWeight([rps], stks, test_query, ref_stk=sm["sh000001"])

set_log_level(LOG_LEVEL.ERROR)


def on_bar(stg: Strategy):
    today = stg.today()
    scores = mf.get_scores(today)
    will_stks = [s.stock for s in scores if s.value > 0][:30]

    positions = stg.tm.get_position_list()
    # 先处理已持仓未选中的，进行卖出
    for p in positions:
        if (not will_stks or p.stock not in will_stks) and p.number > 0:
            k = stg.get_last_kdata(p.stock, 2, Query.DAY)
            if len(k) < 2:  # 防止空数据
                continue

            profit = k[-1].close * p.number - p.buy_money
            profit_ratio = profit / p.buy_money
            stg.order(p.stock, -constant.max_double)
            print(
                f'{today.ymd} 卖出: {p.stock.market_code}{p.stock.name} {p.number}股 {profit:<.2f}, 获利比率: {profit_ratio*100:<.2f}%')
            # if profit_ratio < 0.1:
            #     stg.sell(p.stock, k[-1].close, constant.max_double)
            #     print(
            #         f'{today.ymd} 卖出: {p.stock.market_code}{p.stock.name} {p.number}股 {profit:<.2f}, 获利比率: {profit_ratio*100:<.2f}%')

    for stk in will_stks:
        k = stg.get_last_kdata(stk, 2, Query.DAY)
        if len(k) < 2:  # 防止空数据
            continue

        position = stg.tm.get_position(stg.today(), stk)
        if position.number == 0:
            stg.order(stk, 100)

        # elif position.number > 0 and (stg.today() - position.take_datetime) > Days(3):
        #     stg.sell(stk, k[0].close, position.number)
        # elif (k[0].close * position.number - position.buy_money) / position.buy_money > 0.25:
        #         stg.sell(stk, k[-1].close, position.number)
        # elif (k[0].close * position.number - position.buy_money) / position.buy_money < -0.05:
        #         stg.sell(stk, k[-1].close, position.number)


my_tm = crtTM(start_date, init_cash=100000, cost_func=TC_FixedA2017())
backtest(on_bar, my_tm, start_date, end_date, ktype=Query.DAY)


print(my_tm)
my_tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
my_tm.performance(test_query, sm['sh000001'])
my_tm.heatmap(start_date)

plt.show()
