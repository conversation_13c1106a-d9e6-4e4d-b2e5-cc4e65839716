#!/usr/bin/env python
# -*- coding:utf-8 -*-

import os
import sys
if sys.platform == 'win32':
    os.system('chcp 65001')

from xtquant.xttype import StockAccount
from hikyuu import *

# 获取qmt订单代理
QMT_ACCOUNT = ""  # 字符串
QMT_PATH = r'D:\国金QMT交易端模拟\userdata_mini'  # 自己的QMT交易终端地址
stragegy_name = "大小盘趋势轮动"
qmt_broker = get_part("star.other.broker_qmt",
                      account=QMT_ACCOUNT, path=QMT_PATH, stg_name=stragegy_name)
qmt_broker = crtOB(qmt_broker)

# 创建交易成本计算方法, 用于内部预先计算，可以实际高一些，避免出现现金不足买入失败
cost_func = TC_FixedA2017()

# 交易标的
stk_codes = ['sh510300', 'sz159915']


def my_func(stg: Strategy):
    hku_info("策略执行************************")
    stks = [sm[code] for code in stk_codes]
    my_pf = get_part("star.pf.大小盘趋势轮动", tm=crtTM(), stks=stks, adjust_cycle=10)
    run_in_strategy(pf=my_pf, query=Query(Datetime(********)),
                    broker=qmt_broker, cost_func=cost_func, other_brokers=[])


if __name__ == '__main__':
    s = Strategy(stk_codes,  [Query.DAY])

    # 每交易日 14点52分 执行
    s.run_daily_at(my_func, TimeDelta(0, 14, 52))
    s.start()
