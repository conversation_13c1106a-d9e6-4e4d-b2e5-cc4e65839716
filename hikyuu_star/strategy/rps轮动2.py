#!/usr/bin/env python
# -*- coding:utf-8 -*-


import matplotlib.pylab as plt
import os
import sys
if sys.platform == 'win32':
    os.system('chcp 65001')

from hikyuu import *


stk_codes = ['SH688506',
             'SH688271',
             'SH688187',
             'SH688169',
             'SH688082',
             'SH688036',
             'SH688012',
             'SH688008',
             'SH605499',
             'SH603993',
             'SH603833',
             'SH603806',
             'SH688472',
             'SH603501',
             'SH688256',
             'SH603392',
             'SH603369',
             'SH603296',
             'SH603288',
             'SH603195',
             'SH603019',
             'SH601995',
             'SH601989',
             'SH601988',
             'SH601939',
             'SH601916',
             'SH601901',
             'SH688303',
             'SH601899',
             'SH601898',
             'SH601888',
             'SH688981',
             'SH688396',
             'SH601877',
             'SH601816',
             'SH601800',
             'SH601799',
             'SH601766',
             'SH601728',
             'SH601698',
             'SH601668',
             'SH601658',
             'SH601633',
             'SH601618',
             'SH601600',
             'SH688041',
             'SH601398',
             'SH601390',
             'SH601360',
             'SH601336',
             'SH601319',
             'SH601236',
             'SH601229',
             'SH601225',
             'SH601211',
             'SH601186',
             'SH601169',
             'SH601166',
             'SH601136',
             'SH603986',
             'SH601127',
             'SH601117',
             'SH601100',
             'SH601088',
             'SH601059',
             'SH601021',
             'SZ000858',
             'SH601009',
             'SH688223',
             'SH600660',
             'SH600900',
             'SH600019',
             'SH600887',
             'SH600886',
             'SH600989',
             'SH600875',
             'SH600809',
             'SH600803',
             'SH600795',
             'SZ300782',
             'SH600741',
             'SH601238',
             'SH600547',
             'SH600600',
             'SH600570',
             'SZ300316',
             'SH600515',
             'SZ003816',
             'SH600489',
             'SH601058',
             'SH600460',
             'SH600438',
             'SZ000596',
             'SH600893',
             'SZ002271',
             'SZ002475',
             'SH600690',
             'SH688599',
             'SH600415',
             'SH600377',
             'SH688126',
             'SH600362',
             'SH600346',
             'SH688111',
             'SH600061',
             'SH600332',
             'SH600588',
             'SH601377',
             'SH600276',
             'SH600219',
             'SH600030',
             'SH600196',
             'SH600845',
             'SZ002179',
             'SH600519',
             'SH600039',
             'SH600111',
             'SH600104',
             'SZ300628',
             'SH600089',
             'SH601857',
             'SH600085',
             'SZ002714',
             'SH600183',
             'SH600066',
             'SH600941',
             'SH601808',
             'SH600036',
             'SH600029',
             'SH600028',
             'SH600309',
             'SH600027',
             'SH600176',
             'SH600026',
             'SH601985',
             'SH600025',
             'SH600023',
             'SH688009',
             'SH601788',
             'SH600018',
             'SH601699',
             'SZ300661',
             'SZ300274',
             'SH600011',
             'SH600009',
             'SH601601',
             'SZ000983',
             'SZ301269',
             'SH601688',
             'SZ300896',
             'SZ300418',
             'SZ300760',
             'SZ300759',
             'SZ300750',
             'SH601318',
             'SZ000895',
             'SZ300502',
             'SH600905',
             'SH601838',
             'SZ300442',
             'SZ002371',
             'SH600958',
             'SH601066',
             'SZ002463',
             'SH603260',
             'SZ300413',
             'SH601669',
             'SZ000876',
             'SZ300394',
             'SH600919',
             'SH600584',
             'SH600115',
             'SZ002241',
             'SZ300832',
             'SZ000792',
             'SZ300308',
             'SZ002916',
             'SZ002230',
             'SZ000800',
             'SH601998',
             'SZ002459',
             'SZ300408',
             'SZ002938',
             'SH601872',
             'SZ300122',
             'SZ002920',
             'SZ002812',
             'SH601328',
             'SZ002736',
             'SZ300124',
             'SZ002252',
             'SZ002460',
             'SZ000786',
             'SH605117',
             'SH603799',
             'SH600926',
             'SH600010',
             'SH601818',
             'SH600000',
             'SH603659',
             'SZ002648',
             'SZ002028',
             'SH600031',
             'SZ002236',
             'SH600048',
             'SZ000625',
             'SZ000661',
             'SZ300033',
             'SH600760',
             'SZ002180',
             'SH600160',
             'SZ002142',
             'SZ300450',
             'SZ000538',
             'SZ002129',
             'SH601878',
             'SZ002422',
             'SZ002074',
             'SZ000166',
             'SZ300059',
             'SZ002466',
             'SH600161',
             'SZ002049',
             'SZ000001',
             'SH601689',
             'SZ002050',
             'SH600188',
             'SZ002001',
             'SZ300015',
             'SH601006',
             'SZ002027',
             'SZ000338',
             'SZ002555',
             'SZ001979',
             'SH600938',
             'SH600918',
             'SH601868',
             'SH600372',
             'SH600016',
             'SZ002311',
             'SH600150',
             'SH600585',
             'SZ000999',
             'SH601288',
             'SZ002493',
             'SZ001965',
             'SH600406',
             'SH600015',
             'SH601111',
             'SZ300347',
             'SZ000977',
             'SH601607',
             'SZ000157',
             'SZ300979',
             'SZ300433',
             'SZ000425',
             'SZ002601',
             'SZ000708',
             'SZ001289',
             'SZ000100',
             'SH600999',
             'SZ000807',
             'SH601881',
             'SZ002352',
             'SZ002415',
             'SZ000776',
             'SZ002007',
             'SZ000617',
             'SZ002594',
             'SH600482',
             'SZ000002',
             'SZ000975',
             'SZ000963',
             'SH601628',
             'SZ000651',
             'SH600050',
             'SZ000333',
             'SZ000568',
             'SZ000630',
             'SH600745',
             'SZ000768',
             'SH600436',
             'SH601012',
             'SZ000725',
             'SZ300014',
             'SZ002709',
             'SH601919',
             'SH601138',
             'SH600674',
             'SH601865',
             'SZ000301',
             'SH600233',
             'SZ300999',
             'SZ000938',
             'SH603259',
             'SZ002304',
             'SH600426',
             'SZ000063',
             'SZ300498',
             'SZ000408']


class Config:
    def __init__(self):
        self.stk_codes = stk_codes
        self.rps = get_part('star.ind.rps')
        self.start_date = Datetime(20100101)
        self.mf_start_date = Datetime(20100101)
        self.end_date = None
        self.stks = []
        self.mf = None


config = Config()

set_log_level(LOG_LEVEL.ERROR)


def on_bar(stg: Strategy):
    # stg.tm.fetch_asset_info_from_broker(broker, )
    # print(stg.today())
    if not config.stks:
        config.stks = [sm[code] for code in config.stk_codes]
    if config.mf is None:
        print(len(config.stks))
        config.rps = get_part('star.ind.rps', N1=50,
                              N2=120, N3=250, M=10, ref_stk=sm['sh000001'])
        # inds = [ROCP(CLOSE(), i) for i in [10, 20, 50, 120, 250]]
        config.mf = MF_EqualWeight(
            [(config.rps)], config.stks, Query(config.mf_start_date, config.end_date), ref_stk=sm["sh000001"])
        print(config.mf)

    if config.mf is None:
        print('mf is None```````````````')
        return

    scores = config.mf.get_scores(stg.today())
    will_stks = [s.stock for s in scores if not isnan(
        s.value) and s.value > 0][:50]
    if not will_stks:
        return

    for stk in will_stks:
        k = stg.get_last_kdata(stk, stg.today(), Query.DAY)
        if len(k) == 0:  # 防止空数据
            continue
        position = stg.tm.get_position(stg.today(), stk)
        if position.number == 0:
            stg.order(stk, 100)
        elif position.number > 0 and (stg.today() - position.take_datetime) > Days(10):
            stg.order(stk, -constant.max_double)
        # elif (k[0].close * position.number - position.buy_money) / position.buy_money > 0.50:
        #     stg.sell(stk, k[0].close, position.number)
        # elif ((k[0].close * position.number - position.buy_money) / position.buy_money) < -0.1:
        #     stg.sell(stk, k[0].close, position.number)


if __name__ == '__main__':
    stg_context = StrategyContext(
        stock_list=stk_codes, ktype_list=[Query.DAY,])

    start_date = Datetime(20200101)

    test_mode = True
    if not test_mode:
        print('******************')
        s = Strategy(stg_context)
        s.tm = crtTM(start_date, init_cash=100000, cost_func=TC_FixedA2017())
        s.tm.add
        # 每交易日 14点52分 执行
        s.run_daily_at(on_bar, TimeDelta(0, 14, 52))
        s.start()

    end_date = Datetime(20250601)

    config.end_date = end_date

    my_tm = crtTM(start_date, init_cash=100000, cost_func=TC_FixedA2017())
    backtest(stg_context, on_bar, my_tm, start_date, end_date, ktype=Query.DAY)
    print(my_tm)
    my_tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
    my_tm.performance(Query(start_date, end_date), sm['sh000001'])
    my_tm.heatmap(start_date, end_date)

    plt.show()
