
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20250514"


def part():
    """doc"""


def part(N1=50, N2=120, N3=250, ref_stk=None):
    """doc"""
    STOCKC = CLOSE()
    # STOCKRATE1 = (STOCKC/REF(STOCKC, N1)-1)*100
    # STOCKRATE2 = (STOCKC/REF(STOCKC, N2)-1)*100
    # STOCKRATE3 = (STOCKC/REF(STOCKC, N3)-1)*100
    STOCKRATE1 = ROC(STOCKC, N1)
    STOCKRATE2 = ROC(STOCKC, N2)
    STOCKRATE3 = ROC(STOCKC, N3)
    if ref_stk is None:
        ref_stk = sm['sh000001']
    # {计算大盘涨跌幅}

    ref_k = ref_stk.get_kdata(Query(-1))
    INDEXC = CONTEXT(CLOSE(ref_k))
    INDEXRATE1 = CONTEXT(STOCKRATE1(ref_k))  # (INDEXC/REF(INDEXC,N1)-1)*100
    # (INDEXC/REF(INDEXC,N2)-1)*100;  {中期大盘涨跌幅}
    INDEXRATE2 = CONTEXT(STOCKRATE2(ref_k))
    # (INDEXC/REF(INDEXC,N3)-1)*100;  {长期大盘涨跌幅}
    INDEXRATE3 = CONTEXT(STOCKRATE3(ref_k))

    # {计算相对强度}
    RPS1 = (STOCKRATE1-INDEXRATE1)  # {短期相对强度}
    RPS2 = (STOCKRATE2-INDEXRATE2)  # {中期相对强度}
    RPS3 = (STOCKRATE3-INDEXRATE3)  # {长期相对强度}

    # {计算RPS排名百分比 - 使用横向排名}
    RPSPER1 = 100*(1+RPS1/100)  # {短期RPS百分比}
    RPSPER2 = 100*(1+RPS2/100)  # {中期RPS百分比}
    RPSPER3 = 100*(1+RPS3/100)  # {长期RPS百分比}

    # {加权平均RPS - 短期权重20%，中期权重30%，长期权重50%}
    RPS = (RPSPER1*0.2 + RPSPER2*0.3 + RPSPER3*0.5)
    RPS.name = "rps"
    return RPS


if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        ind = part()
        print(ind)
        exit(0)

    import sys
    if sys.platform == 'win32':
        import os
        os.system('chcp 65001')

    # 仅加载测试需要的数据，请根据需要修改
    options = {
        'stock_list': ['sz000001'],
        'ktype_list': ['day'],
        'preload_num': {'day_max': 100000},
        'load_history_finance': False,
        'load_weight': False,
        'start_spot': False,
        'spot_worker_num': 1,
    }
    load_hikyuu(**options)

    stks = tuple([sm[code] for code in options['stock_list']])

    # 请在下方编写测试代码
    ind = part()
    print(ind)

    local_hub = get_current_hub(__file__)
    update_hub(local_hub)

    stk = sm['sz000001']
    k = stk.get_kdata(Query(-300))
    ind(k).plot()

    # 显示图形
    import matplotlib.pylab as plt
    plt.show()
