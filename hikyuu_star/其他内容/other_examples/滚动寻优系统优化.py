#!/usr/bin/python
# -*- coding: utf8 -*-
#
# Create on: 2024-10-16
#    Author: fasiondog

from matplotlib import pylab as plt
from itertools import product
import os
import sys
if sys.platform == 'win32':
    os.system('chcp 65001')

stk_code = "sz000002"
os.environ['HKU_STOCK_LIST'] = stk_code  # 仅加载指定的证券
os.environ['HKU_KTYPE_LIST'] = 'day'  # 加载K线类型（同时包含加载顺序）
# os.environ['HKU_LOAD_STOCK_WEIGHT'] = '0'  # 禁止加载权息数据
# os.environ['HKU_LOAD_HISTORY_FINANCE'] = '0'  # 禁止加载历史财务信息
os.environ['HKU_START_SPOT'] = '0'  # 禁止启动行情接收代理

from hikyuu.interactive import *  # NOQA: E402

fast_n_range = range(5, 80, 1)
slow_n_range = range(50, 250, 1)
params = [v for v in product(fast_n_range, slow_n_range)]
params = [(fast_n, slow_n) for fast_n, slow_n in params if fast_n < slow_n]
print(f"参数组合数量: {len(params)}")

sys_list = []
for x, y in params:
    # 创建趋势双均线系统实例，各自测试账户初始资金为 1000000，无止损/止盈/资金控制/交易成本
    my_sys = get_part('default.sys.趋势双均线', fast_n=x,
                      slow_n=y, tm=crtTM(init_cash=100000), mm=MM_Nothing())
    my_sys.name = f'{my_sys.name}_{x}_{y}'
    sys_list.append(my_sys)


@spend_time
def find_optimal_param(sys_list, stk, query, key=None):
    """
    该函数用于寻找系统列表中具有最高指定性能指标的系统。

    参数:
        sys_list (list): 系统对象的列表。
        stk (object): 用于系统运行的股票数据对象。
        query (object): 查询对象，用于系统运行。
        key (str, optional): 性能指标的键，默认为'帐户平均年收益率%'。

    返回:
        tuple: 包含最高性能指标值和对应系统的元组。

    异常:
        Exception: 如果提供的性能指标键无效，则抛出异常。
    """
    if key is None:
        key = '帐户平均年收益率%'
    if not Performance.exist(key):
        raise Exception(f"Invalid key: {key}")

    dates = sm.get_trading_calendar(query)
    last_date = dates[-1]  # 用于统计绩效的截止日期

    max_val = -10000
    max_sys = None
    for my_sys in sys_list:
        my_sys.run(stk, query)
        per = Performance()
        per.statistics(my_sys.tm, last_date)
        val = per[key]
        if val > max_val:
            max_val = val
            max_sys = my_sys
    return max_val, max_sys


# =============================================
# 以20010101-20100101 作为参数寻优空间
# =============================================
stk = sm[stk_code]
start_date = Datetime(20010101)
end_date = Datetime(20100101)
query = Query(start_date, end_date)
max_val, max_sys = find_optimal_param(sys_list, stk, query, key='当前总资产')

# 获取最优参数
tmp = max_sys.name.split('_')
fast_n, slow_n = int(tmp[1]), int(tmp[2])
print(f"最优参数, fast_n: {fast_n}, slow_n: {slow_n}")


# =============================================
# 以20100101-20240930 作为测试区间
# =============================================
start_date = Datetime(20100101)
end_date = Datetime(20240930)
query = Query(start_date, end_date)

# 滚动寻优系统
my_sys = SYS_WalkForward(sys_list, crtTM(), train_len=300, test_len=200)
my_sys.name = "滚动寻优系统"
# my_sys.set_param("parallel", True)
# my_sys.set_param("trace", True)
my_sys.run(stk, query)
my_sys.performance()
# plt.show()

# 以2001-2010选出的最优参数进行普通系统测试
x = fast_n
y = slow_n
my_sys = get_part('default.sys.趋势双均线', fast_n=x,
                  slow_n=y, tm=crtTM(init_cash=100000), mm=MM_Nothing())
my_sys.name = f'{my_sys.name}_{x}_{y}'
my_sys.run(stk, query)
my_sys.performance()
plt.show()
