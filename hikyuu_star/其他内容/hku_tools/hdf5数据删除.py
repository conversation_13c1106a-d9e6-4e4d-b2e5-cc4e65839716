# HikyuuTdx 导入数据时进行了数据校验，但无法避免节假日后第一天可能出现的数据错误，这部分错误会在第二天导入时报错，
# 此时需要手工删除错误数据


# 简易版本，需自行修改，手工指定删除的数据
# from hikyuu import *
# importer = KDataToHdf5Importer()
# # 设置 hdf5 所在文件路径
# importer.set_config("/home/<USER>/stock", ["SH", "SZ", "BJ"])
# # 删除指定股票指定K线类型从 datetime 开始的数据
# # K线类型仅为 Query.DAY，Query.MIN5, Query.MIN (其他为扩展数据会自动一起清理)
# # 5分钟、分钟线导入时报错的时间是最后一条数据，但删除时建议最后一整天的都删除，就是这里的 datetime 指定到天
# importer.remove("SH", "880005", Query.DAY, Datetime(20150101))


# 感谢来自 zhushunyi 分享，根据导入时出错的日志自动提前删除错误数据

from hikyuu import *
import re
from tqdm import tqdm
importer = KDataToHdf5Importer()
# 设置 hdf5 所在文件路径
importer.set_config("F:\\stock_data_new_20250318", ["SH", "SZ", "BJ"])


def extract_stock_data(file_path):
    pattern = re.compile(r'(\d{12})\s(DAY|1MIN)\s(S[HZ]\d{6})')
    results = []

    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            matches = pattern.findall(line)
            if matches:
                for match in matches:
                    results.append(f"{match[0]} {match[1]} {match[2]}")

    return results


# 示例：读取日志文件并提取数据
file_path = 'record0814.txt'  # 替换为你的日志文件路径
extracted_data = extract_stock_data(file_path)

# 删除指定股票指定K线类型从 datetime 开始的数据
# K线类型仅为 Query.DAY，Query.MIN5, Query.MIN (其他为扩展数据会自动一起清理)
# 5分钟、分钟线导入时报错的时间是最后一条数据，但删除时建议最后一整天的都删除，就是这里的 datetime 指定到天
for data in tqdm(extracted_data):
    err_data = (data.split())
    print(err_data)
    if err_data[1] == '1MIN':
        importer.remove(err_data[2][:2], err_data[2][2:],
                        Query.MIN, Datetime(int(err_data[0][:-4])))
    elif err_data[1] == '5MIN':
        importer.remove(err_data[2][:2], err_data[2][2:],
                        Query.MIN5, Datetime(int(err_data[0][:-4])))
    elif err_data[1] == 'DAY':
        importer.remove(err_data[2][:2], err_data[2][2:],
                        Query.DAY, Datetime(int(err_data[0][:-4])))
    else:
        print(err_data, "no fixed !")
