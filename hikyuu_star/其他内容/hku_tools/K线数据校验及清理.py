from hikyuu.interactive import *
import tables as tbl
import akshare as ak


@hku_catch(None, callback=lambda stk, date: print("failed fetch from ak:", stk.market_code, stk.name))
@timeout(60)
def get_from_ak(stk, date):
    '''从 akshare 获取指定日期的数据'''
    start_date = str(date.ymd)
    end_date = start_date
    if stk.market_code[:5] == "SH880":
        # 通达信自身定义的指数，非标准
        return None

    if stk.type in (constant.STOCKTYPE_A, constant.STOCKTYPE_A_BJ, constant.STOCKTYPE_GEM, constant.STOCKTYPE_START):
        a = ak.stock_zh_a_hist(
            stk.code, 'daily', start_date, end_date, adjust="")
        if not a.empty:
            return (a['开盘'][0], a['最高'][0], a['最低'][0], a['收盘'][0], a['成交额'][0] * 0.001, a['成交量'][0])
    elif stk.type == constant.STOCKTYPE_ETF:
        a = ak.fund_etf_hist_em(
            stk.code, 'daily', start_date, end_date, adjust="")
        if not a.empty:
            return (a['开盘'][0], a['最高'][0], a['最低'][0], a['收盘'][0], a['成交额'][0] * 0.001, a['成交量'][0])
    elif stk.type == constant.STOCKTYPE_FUND:
        a = ak.fund_lof_hist_em(
            stk.market_code, 'daily', start_date, end_date, adjust="")
        if not a.empty:
            return (a['开盘'][0], a['最高'][0], a['最低'][0], a['收盘'][0], a['成交额'][0] * 0.001, a['成交量'][0])
    elif stk.type == constant.STOCKTYPE_INDEX:
        a = ak.stock_zh_index_daily_em(
            stk.market_code.lower(), start_date, end_date)
        if not a.empty:
            return (
                a['open'][0], a['high'][0], a['low'][0], a['close'][0], a['amount'][0] * 0.001, a['volume'][0] * 0.01)
    else:
        a = ak.stock_zh_a_daily(stk.market_code.lower(),
                                start_date, end_date, adjust="")
        if not a.empty:
            return (
                a['open'][0], a['high'][0], a['low'][0], a['close'][0], a['amount'][0] * 0.001, a['volume'][0] * 0.01)
    return None


def check_stock_data(stk, start_date, end_date=Datetime(), ignore_key_error=True):
    print(f'{stk.market_code} {stk.name}')
    if stk.code[:3] == '880':
        return None
    limit = 0.12
    if stk.type in (constant.STOCKTYPE_GEM, constant.STOCKTYPE_START, constant.STOCKTYPE_INDEX):
        limit = 0.22
    k = stk.get_kdata(Query(start_date, end_date))
    s_ret = [f"{stk.market.lower()}_day.{stk.code}", []]
    for i in range(1, len(k)):
        if k[i - 1].close <= 0.0 or abs(k[i].close - k[i - 1].close) / k[i - 1].close > limit:
            weight = stk.get_weight(k[i].datetime, k[i].datetime.next_day())
            if not weight:
                a = get_from_ak(stk, k[i].datetime)
                if a is not None:
                    if (abs(a[3] - k[i].close) / a[3]) >= 0.01 and abs(k[i].close - a[3]) > 0.0055:
                        print(stk.market_code, stk.name,
                              k[i].datetime, k[i], a)
                        s_ret[1].append(
                            (a[0], a[1], a[2], a[3], a[4], a[5], k[i].datetime.ymdhm))
                elif not ignore_key_error:
                    s_ret[1].append(
                        (None, None, None, None, None, None,  k[i].datetime.ymdhm))

    return s_ret if len(s_ret[1]) > 0 else None


@spend_time
def check_data(stk_list, start_date, end_date=Datetime(), ignore_key_error=True):
    from concurrent import futures
    # 如果需要，此处可以手工自行修改线程池数量，加快检查速度
    with futures.ThreadPoolExecutor() as executor:
        res = executor.map(lambda item: check_stock_data(item[0], item[1], item[2], ignore_key_error),
                           [(s, start_date, end_date) for s in stk_list], timeout=120)
    res = [v for v in res if v is not None]

    err_count = 0
    for v in res:
        err_count += len(v[1])
    print("err_count:", err_count)
    return res


@hku_catch()
def delete_mysql_data(connect, start_date, market, code, ktype):
    if ktype == Query.DAY:
        table_name = f'`{market.lower()}_day`.`{code}`'
    elif ktype == Query.WEEK:
        table_name = f'`{market.lower()}_week`.`{code}`'
    elif ktype == Query.MONTH:
        table_name = f'`{market.lower()}_month`.`{code}`'
    elif ktype == Query.QUARTER:
        table_name = f'`{market.lower()}_quarter`.`{code}`'
    elif ktype == Query.HALFYEAR:
        table_name = f'`{market.lower()}_halfyear`.`{code}`'
    elif ktype == Query.YEAR:
        table_name = f'`{market.lower()}_year`.`{code}`'
    elif ktype == Query.MIN:
        table_name = f'`{market.lower()}_min`.`{code}`'
    elif ktype == Query.MIN5:
        table_name = f'`{market.lower()}_min5`.`{code}`'
    elif ktype == Query.MIN15:
        table_name = f'`{market.lower()}_min15`.`{code}`'
    elif ktype == Query.MIN30:
        table_name = f'`{market.lower()}_min30`.`{code}`'
    elif ktype == Query.MIN60:
        table_name = f'`{market.lower()}_min60`.`{code}`'
    elif ktype == Query.HOUR2:
        table_name = f'`{market.lower()}_hour2`.`{code}`'
    else:
        raise "Not support!"

    cur = connect.cursor()
    sql = f"delete from {table_name} where date >={start_date}"
    # print(sql)
    cur.execute(sql)
    cur.close()


def clean_mysql_data(connect, check_ret):
    # 直接用爬虫检查的结果更新日线数据
    sql = "UPDATE {} set open={}, high={}, low={}, close={}, amount={}, count={} where date={}"
    cur = connect.cursor()
    for item in check_ret:
        for v in item[1]:
            if v[0] is not None:
                print(sql.format(item[0], v[0],
                                 v[1], v[2], v[3], v[4], v[5], v[6]))
                cur.execute(sql.format(item[0], v[0],
                                       v[1], v[2], v[3], v[4], v[5], v[6]))
            else:
                print(f"will remove {item[0]} start: {v[6]}")

    cur.close()

    from hikyuu.data.common_mysql import update_extern_data

    # 对网络数据取不到的，同时删除日线
    for item in check_ret:
        if item[1][0][0] is None:
            market = item[0][:2]
            code = item[0][-6:]
            start_date = item[1][0][6]
            delete_mysql_data(connect, start_date, market, code, Query.DAY)

    # 删除日线以上的数据， 并重新合成
    up_day = [Query.WEEK, Query.MONTH,
              Query.HALFYEAR, Query.YEAR, Query.QUARTER]
    for item in check_ret:
        for ktype in up_day:
            market = item[0][:2]
            code = item[0][-6:]
            start_date = item[1][0][6]
            delete_mysql_data(connect, start_date, market, code, ktype)

        # 重新生成日线级别索引
        update_extern_data(connect, market.upper(), code, "DAY")

    connect.commit()


# format off
if __name__ == "__main__":
    # 注：比下面校验修改更快的方法是用重新设定自己的通达信服务器地址，然后删除所有数据重新导入

    # 由于网络爬虫获取数据较大，请设置起始日期，从指定起始日期开始检查
    # 初次可以检查全部，后续从指定日期开始定期检查

    start_date = Datetime(20250101)
    end_date = None  # Datetime(20000101)
    # stks = [s for s in sm if s.type in (constant.STOCKTYPE_FUND,)]
    stks = [s for s in sm]
    # stks = [sm['sz163503']]

    ignore_key_error = True  # 忽略网络获取不到的数据但可能错误的数据，否则删除
    ret = check_data(stks, start_date, end_date, ignore_key_error)

    # 以下仅支持使用 MYSQL 存储的数据清理，请自行修改 mysql 相关参数
    # 如果使用的是 HsDF5 数据存储，请注释掉
    # 清理 mysql 数据，只处理 日线及其之上的 K 线数据
    # 使用网络数据代替存在错误的数据，并重新合成日线之上的 K 线数据

    from configparser import ConfigParser
    dev_config = ConfigParser()
    dev_config.read(os.path.expanduser("~") + '/workspace/dev.ini')
    db = 'mysql57'
    user = dev_config.get(db, 'user')
    password = dev_config.get(db, 'pwd')
    host = dev_config.get(db, 'host')
    port = dev_config.getint(db, 'port')

    import mysql.connector
    connect = mysql.connector.connect(
        user=user, password=password, host=host, port=port)

    clean_mysql_data(connect, ret)

    connect.commit()
    connect.close()
