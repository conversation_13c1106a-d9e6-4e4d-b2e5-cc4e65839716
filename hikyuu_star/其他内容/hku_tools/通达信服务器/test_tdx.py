#!/usr/bin/python
# -*- coding: utf8 -*-
#
# Create on: 2024-10-21
#    Author: fasiondog

# -------------------------------------------------------------
# 测试最优的服务器
# 1. 请先在用户目录下 .hikyuu 目录下创建自己的 hosts.py
#    (或者 copy 本目录下的 tdx_hosts.py 后，改名为 hosts.py)
# 2. 根据打印修改 hosts.py，注释掉比较慢或者不稳定的服务器
#    建议保留平均连接耗时小于 0.17 的服务器
# -------------------------------------------------------------

from hikyuu.data.common_pytdx import search_best_tdx, hq_hosts

t = {}
for h in hq_hosts:
    t[h[1]] = [0, 0.0, 0.0]  # count, sum, avg

test_count = 20
for i in range(test_count):
    x = search_best_tdx()
    for v in x:
        t[v[2]][0] += 1
        t[v[2]][1] += v[1]
        t[v[2]][2] = t[v[2]][1] / t[v[2]][0]

a = {}
for h in hq_hosts:
    a[h[1]] = h[0]

xt = []
for k, v in t.items():
    if v[0] >= test_count:
        xt.append([a[k], v[0], v[1], v[2]])

xt.sort(key=lambda v: v[2])

print("-----------------------------------")
print(f"Best: {len(xt)}")
for i, v in enumerate(xt):
    print(i, f'{v[3]:<.4f}', v[1], v[0])

tmp = set()
for v in xt:
    tmp.add(v[0])
print("-----------------------------------")
print(f"Could't connect: {len(hq_hosts) - len(xt)}")
for h in hq_hosts:
    if h[0] not in tmp:
        print(h[0])
