#!/usr/bin/python
# -*- coding: utf8 -*-
#
# Create on: 20240102
#    Author: fasiondog

import struct
import pandas as pd
from hikyuu.data.common import modifiy_code
from hikyuu.util import *


def read_file_loc(file_name, splits):
    with open(file_name, 'r') as f:
        buf_lis = f.read().split('\n')
    return [x.split(splits) for x in buf_lis[:-1]]


def get_block_zs_tdx_loc(tdx_cache_path, block='hy'):
    buf_line = read_file_loc(f'{tdx_cache_path}/tdxzs3.cfg', '|')
    mapping = {'hy': '2', 'dq': '3', 'gn': '4',
               'fg': '5', 'yjhy': '12', 'zs': '6'}
    df = pd.DataFrame(buf_line, columns=[
                      'name', 'code', 'type', 't1', 't2', 'block'])
    dg = df.groupby(by='type')
    if (block == 'zs'):
        return df
    temp = dg.get_group(mapping[block]).reset_index(drop=True)
    temp.drop(temp.columns[[2, 3, 4]], axis=1, inplace=True)
    return temp


def get_block_file(tdx_cache_path, block='gn'):
    file_name = f'block_{block}.dat'
    with open(f'{tdx_cache_path}/{file_name}', 'rb') as f:
        buff = f.read()
    head = struct.unpack('<384sh', buff[:386])
    blk = buff[386:]
    blocks = [blk[i * 2813:(i + 1) * 2813] for i in range(head[1])]
    bk_list = []
    for bk in blocks:
        name = bk[:8].decode('gbk').strip('\x00')
        num, t = struct.unpack('<2h', bk[9:13])
        stks = bk[13:(12 + 7 * num)].decode('gbk').split('\x00')
        bk_list = bk_list + [[name, block, num, stks]]
    return pd.DataFrame(bk_list, columns=['name', 'tp', 'num', 'stocks'])

# 文件头：384字节
# 板块个数：2字节
# 各板块数据存储结构(紧跟板块数目依次存放)，
# 每个板块占据的存储空间为2813个字节，可最多包含400只个股
# 板块名称：9字节
# 该板块包含的个股个数：2字节
# 板块类别：2字节
# 该板块下个股代码列表(连续存放，直到代码为空)
# 个股代码：7字节


def gn_block(tdx_cache_path, blk='gn'):
    del_row = {'gn': ['��GDR'], 'fg': [
        '����ͨSH', '���ͨSZ', '������ȯ'], 'zs': [''], }
    mapping = {'gn': {
        '�����뵼': '�������뵼��',
        'Ԫ����': 'Ԫ�������',
        '������': '����������',
        '���ž�': '�����ݸ˾�',
        '�¹�ҩ': '�¹�ҩ����',
        '�л���': '�л������',
        '��� ���': '����Ⱦ����',
        '�������': '�������֤',
        'װ�佨��': 'װ��ʽ����'
    }, 'fg': {
        '���Ͻ�': '���Ͻ�ֹ�',
        '�½��ɷ�': '�½�ָ���',
    },
        'zs': {''

               }, }

    bf = get_block_file(tdx_cache_path, blk)
    bf.drop(bf[bf['name'].isin(del_row[blk])].index, inplace=True)
    bf['name'] = bf['name'].replace(mapping[blk], regex=True)

    t = get_block_zs_tdx_loc(tdx_cache_path, blk)

    if (blk == 'zs'):
        # print(bf)
        return bf
    del t['block']
    df = pd.merge(t, bf, on='name')
    return df


def hy_block(tdx_cache_path, blk='hy'):
    stocklist = get_stock_hyblock_tdx_loc(tdx_cache_path)
    blocklist = get_block_zs_tdx_loc(tdx_cache_path, blk)
    blocklist['block5'] = blocklist['block'].str[0:5]
    blocklist['num'] = 0
    blocklist['stocks'] = ''
    for i in range(len(blocklist)):
        blockkey = blocklist.iat[i, 2]
        if (len(blockkey) == 5):
            datai = stocklist[stocklist['block5'] == blockkey]  # 根据板块名称过滤
        else:
            datai = stocklist[stocklist['block'] == blockkey]  # 根据板块名称过滤
        # 板块内进行排序填序号
        datai = datai.sort_values(by=['code'], ascending=[True])
        # datai.reset_index(drop=True, inplace=True)
        codelist = datai['code'].tolist()
        blocklist.iat[i, 4] = len(codelist)
        blocklist.iat[i, 5] = codelist
    blocklist = blocklist.drop(blocklist[blocklist['num'] == 0].index)
    return blocklist


def get_stock_hyblock_tdx_loc(tdx_cache_path):
    buf_line = read_file_loc(f'{tdx_cache_path}/tdxhy.cfg', '|')
    buf_lis = []
    for x in buf_line:
        buf_lis.append(x)

    df = pd.DataFrame(buf_lis, columns=[
                      'c0', 'code', 'block', 'c1', 'c2', 'c3'])
    df.drop(df.columns[[0, 3, 4, 5]], axis=1, inplace=True)

    df = df[(df['block'] != '')]
    df['block5'] = df['block'].str[0:5]
    return df


def tdx_block_to_sqlite(connect, tdx_cache_path, blk_t='gn'):
    if blk_t == 'gn':
        category = "TDX概念板块"
        df = gn_block(tdx_cache_path, blk_t)
    elif blk_t == 'hy':
        category = 'TDX行业板块'
        df = hy_block(tdx_cache_path, 'hy')
    elif blk_t == 'zs':
        category = 'TDX指数板块'
        df = gn_block(tdx_cache_path, blk_t)
    else:
        print("unsupport blk_t: ", blk_t)
        return

    import sqlite3
    is_sqlite = True if isinstance(connect, sqlite3.Connection) else False

    blks_index = []
    blks = []
    for i in range(len(df)):
        name = df.iloc[i]['name']
        if blk_t != 'zs':
            blks_index.append((category, name, f"SH{df.iloc[i]['code']}"))
        codes = [code for code in df.iloc[i]['stocks'] if code]
        for code in codes:
            ncode = modifiy_code(code) if code != '999999' else 'SH000001'
            if ncode is not None:
                blks.append((category, name, ncode))
            else:
                print(category, name, code)

    cur = connect.cursor()
    if blk_t != 'zs' and blks_index:
        if is_sqlite:
            cur.execute(f"delete from BlockIndex where category='{category}'")
            sql = "insert into BlockIndex (category, name, market_code) values (?, ?, ?)"
            cur.executemany(sql, blks_index)
            sql = f"select stockid, code from Stock where marketid=1 and code in {tuple([v[2][2:] for v in blks_index])}"
        else:
            cur.execute(
                f"delete from `hku_base`.`BlockIndex` where category='{category}'")
            sql = "insert into `hku_base`.`BlockIndex` (category, name, market_code) values (%s, %s, %s)"
            cur.executemany(sql, blks_index)
            sql = f"select stockid, code from `hku_base`.`stock` where marketid=1 and code in {tuple([v[2][2:] for v in blks_index])}"
        # print(sql)
        cur.execute(sql)
        a = cur.fetchall()
        exist_codes = {}
        for item in a:
            exist_codes[item[1]] = item[0]
        insert_stocks = []
        for v in blks_index:
            if v[2][2:] not in exist_codes:
                # marketid, code, name, type, valid, startDate, endDate
                insert_stocks.append(
                    (1, v[2][2:], v[1], 2, 1, 19901219, 99999999))
        if is_sqlite:
            sql = "insert into Stock (marketid, code, name, type, valid, startDate, endDate) values (?,?,?,?,?,?,?)"
        else:
            sql = "insert into `hku_base`.`stock` (marketid, code, name, type, valid, startDate, endDate) values (%s,%s,%s,%s,%s,%s,%s)"
        cur.executemany(sql, insert_stocks)
        connect.commit()

    if blks:
        if is_sqlite:
            cur.execute(f"delete from Block where category='{category}'")
            sql = "insert into Block (category, name, market_code) values (?, ?, ?)"
        else:
            cur.execute(
                f"delete from `hku_base`.`block` where category='{category}'")
            sql = "insert into `hku_base`.`block` (category, name, market_code) values (%s,%s,%s)"
        cur.executemany(sql, blks)
        connect.commit()

    cur.close()


if __name__ == "__main__":
    # 用途：
    # 导入通达信本地板块信息和对应指数
    # 板块分类前统一加上了 TDX 前缀，和现有默认的东方财富板块信息区分
    # 只支持导入通达信的指数、概念、行业板块信息
    # 对于板块指数只支持行业和概念板块，不包含指数板块对应的指数
    #
    # 板块信息更新不需要很频繁，在导入通达信本地板块指数后，
    # 后续使用 HikyuuTDX 下载数据时，会自动下载通达信的板块指数K线数据（通常为 SH880开头）
    # 如:
    #   In [10]: blks = sm.get_block_list("TDX行业板块")
    #   In [11]: blks[0]
    #   Out[11]: Block(TDX行业板块, 交通设施)
    #   In [12]: blks[0].index_stock
    #   Out[12]: Stock(SH, 880465, 交通设施, 指数, 1, 2005-06-07 00:00:00, +infinity)

    # 本地通达信板块信息目录，请自行修改
    tdx_cache = r"D:\zd_huatai\T0002\hq_cache"

    # 使用 hdf5 存储方式的
    import sqlite3
    from hikyuu.data.common_sqlite3 import create_database
    dest_dir = "d:\\stock"
    connect = sqlite3.connect(dest_dir + "/stock.db")
    create_database(connect)

    # 使用 mysql 存储的：
    # import mysql.connector
    # from hikyuu.data.common_mysql import create_database
    # host = "127.0.0.1"
    # port = 3306
    # usr = "root"
    # pwd = ""
    # connect = mysql.connector.connect(user=usr, password=pwd, host=host, port=port)
    # create_database(connect)

    blk_ts = ['zs', 'gn', 'hy']
    for blk in blk_ts:
        tdx_block_to_sqlite(connect, tdx_cache, blk)
    connect.commit()
    connect.close()
