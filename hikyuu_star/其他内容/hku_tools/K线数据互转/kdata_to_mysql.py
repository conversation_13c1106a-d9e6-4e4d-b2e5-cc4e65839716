#!/usr/bin/python
# -*- coding: utf8 -*-

# 1. 直接通过 hikyuu 本身将K线数据写入到指定的mysql
# 2. k线数据源是 hikyuu 本身，也只是导 K 线数据到指定的 mysql
# 3. 通常用于备份到另一个mysql或者将 hdf5 的数据导入到 mysql 的场景。
# 4. 也可以自行修改导入其他数据格式的数据到 mysql，但由于没有股票的基础信息，需要使用
#    该 mysql 数据库作为 hikyuu 数据源时，需要再用 hikyuutdx 导入一遍基础数据

from hikyuu.interactive import *


def get_table(connect, market, code, ktype):
    cur = connect.cursor()
    schema = "{market}_{ktype}".format(market=market, ktype=ktype).lower()
    cur.execute(
        "SELECT 1 FROM information_schema.SCHEMATA where SCHEMA_NAME='{}'".format(schema))
    a = cur.fetchone()
    if not a:
        cur.execute("CREATE SCHEMA `{}`".format(schema))
        connect.commit()

    tablename = code.lower()
    cur.execute(
        "SELECT 1 FROM information_schema.tables "
        "where table_schema='{schema}' and table_name='{name}'".format(
            schema=schema, name=tablename)
    )
    a = cur.fetchone()
    if not a:
        sql = """
                CREATE TABLE `{schema}`.`{name}` (
                    `date` BIGINT(20) UNSIGNED NOT NULL,
                    `open` DOUBLE UNSIGNED NOT NULL,
                    `high` DOUBLE UNSIGNED NOT NULL,
                    `low` DOUBLE UNSIGNED NOT NULL,
                    `close` DOUBLE UNSIGNED NOT NULL,
                    `amount` DOUBLE UNSIGNED NOT NULL,
                    `count` DOUBLE UNSIGNED NOT NULL,
                    PRIMARY KEY (`date`)
                )
                COLLATE='utf8mb4_general_ci'
                ENGINE=MyISAM
                ;
              """.format(schema=schema, name=tablename)
        cur.execute(sql)
        connect.commit()

    cur.close()
    return "`{schema}`.`{name}`".format(schema=schema, name=tablename)


def get_lastdatetime(connect, tablename):
    cur = connect.cursor()
    cur.execute("select max(date) from {}".format(tablename))
    a = cur.fetchone()
    return a[0]


def kdata_to_mysql(conn, stock, ktype, increment=True):
    market = stock.market
    code = stock.code
    table_name = get_table(conn, market, code, ktype)
    print(f"{stock.market_code} {stock.name}: {table_name}")
    if increment:
        last_date = get_lastdatetime(conn, table_name)
        if last_date is None:
            kdata = stock.get_kdata(Query(0, ktype=ktype))
        else:
            kdata = stock.get_kdata(
                Query(Datetime(last_date)+Seconds(1), ktype=ktype))
    else:
        kdata = stock.get_kdata(Query(0, ktype=ktype))
    if len(kdata) == 0:
        return

    buf = [(k.datetime.ymdhm, k.open, k.high, k.low,
            k.close, k.amount, k.volume) for k in kdata]
    sql = (
        "INSERT INTO {tablename} (date, open, high, low, close, amount, count) "
        "VALUES (%s, %s, %s, %s, %s, %s, %s) ON DUPLICATE KEY UPDATE date = date;".format(
            tablename=table_name)
    )
    cur = connect.cursor()
    cur.executemany(sql, buf)
    conn.commit()
    cur.close()


if __name__ == "__main__":
    from configparser import ConfigParser
    dev_config = ConfigParser()
    dev_config.read(os.path.expanduser("~") + '/workspace/dev.ini')
    db = 'mysql55'
    user = dev_config.get(db, 'user')
    password = dev_config.get(db, 'pwd')
    host = dev_config.get(db, 'host')
    port = dev_config.getint(db, 'port')

    import mysql.connector
    connect = mysql.connector.connect(
        user=user, password=password, host=host, port=port)

    # ktypes = [Query.DAY, Query.WEEK, Query.MONTH,
    #           Query.QUARTER, Query.HALFYEAR, Query.YEAR,
    #           Query.MIN, Query.MIN5, Query.MIN15, Query.MIN30, Query.MIN60]
    ktypes = [Query.MIN, Query.MIN5, Query.MIN15, Query.MIN30, Query.MIN60]
    total = len(ktypes) * len(sm)
    count = 0
    for stk in sm:
        for ktype in ktypes:
            kdata_to_mysql(connect, stk, ktype, False)
            count += 1
            print(
                f"total: {total}, finished: {count}, percnet: {count/total*100:.2f}%")

    connect.commit()
    connect.close()
