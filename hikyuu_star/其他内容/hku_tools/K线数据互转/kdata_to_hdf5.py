#!/usr/bin/python
# -*- coding: utf8 -*-
#
# Create on: 2025-07-30
#    Author: fasiondog

from hikyuu.interactive import *


@spend_time
def kdata_to_hdf5(conn, stock, ktype):
    hku_check(ktype in (Query.DAY, Query.MIN5, Query.MIN),
              "only support DAY, MIN5 and MIN ktypes")
    d = conn.get_last_datetime(stock.market, stock.code, ktype)
    if d.is_null():
        d = Datetime.min()
    m = Query.get_ktype_in_min(ktype)
    ks = stock.get_krecord_list(Query(d + Minutes(m), ktype=ktype))
    print(f"{stock.market_code} {stock.name} {ktype}: 记录数: {len(ks)}")
    if len(ks) == 0:
        return

    conn.add_krecord_list(stock.market, stock.code, ks, ktype)
    if ktype in (Query.DAY, Query.MIN5):
        conn.update_index(stock.market, stock.code, ktype)


@spend_time
def timeline_to_hdf5(conn, stock):
    d = conn.get_last_datetime(stock.market, stock.code, "TIMELINE")
    if d.is_null():
        d = Datetime.min()
    ks = stock.get_timeline_list(Query(d + Minutes(1)))
    print(f"{stock.market_code} {stock.name} 分时记录数: {len(ks)}")
    if len(ks) == 0:
        return

    conn.add_timeline_list(stock.market, stock.code, ks)


@spend_time
def transdata_to_hdf5(conn, stock):
    d = conn.get_last_datetime(stock.market, stock.code, "TRANSDATA")
    if d.is_null():
        d = Datetime.min()
    ks = stock.get_trans_list(Query(d + Seconds(1)))
    print(f"{stock.market_code} {stock.name} 分笔记录数: {len(ks)}")
    if len(ks) == 0:
        return

    conn.add_trans_list(stock.market, stock.code, ks)


def backup_to_hdf5(datapath, market, ktype):
    hku_check(ktype in (Query.DAY, Query.MIN5, Query.MIN, 'TIMELINE', 'TRANSDATA'),
              "only support DAY, MIN5, MIN, TIMELINE and TRANSDATA ktypes")
    importer = KDataToHdf5Importer()
    importer.set_config(datapath, [market], [ktype])
    sm = StockManager.instance()
    stocks = [s for s in sm if s.market == market]

    total = len(stocks)
    count = 0
    if ktype == 'TIMELINE':
        for stock in stocks:
            timeline_to_hdf5(importer, stock)
            count += 1
            print(
                f"====================> total: {total}, finished: {count}, percnet: {count/total*100:.2f}%")
        print(f"Backup completed for {market} TIMELINE data.")
        return
    elif ktype == 'TRANSDATA':
        for stock in stocks:
            transdata_to_hdf5(importer, stock)
            count += 1
            print(
                f"====================> total: {total}, finished: {count}, percnet: {count/total*100:.2f}%")
        print(f"Backup completed for {market} TRANSDATA data.")
        return
    else:
        for stock in stocks:
            kdata_to_hdf5(importer, stock, ktype)
            count += 1
            print(
                f"====================> total: {total}, finished: {count}, percnet: {count/total*100:.2f}%")
        print(f"Backup completed for {market} {ktype} data.")


if __name__ == '__main__':
    from multiprocessing import Process
    ktypes = [Query.DAY, Query.MIN5, Query.MIN, 'TIMELINE', 'TRANSDATA']
    # ktypes = [Query.MIN5]
    markets = ['BJ', 'SH', 'SZ']
    # markets = ['BJ']
    plist = []

    # 请注意修改备份目录
    for market in markets:
        for ktype in ktypes:
            # backup_to_hdf5('/Users/<USER>/tmp/hikyuu', market, ktype)
            p = Process(target=backup_to_hdf5, args=(
                '/Users/<USER>/tmp/hikyuu', market, ktype))
            plist.append(p)
            p.start()

    for p in plist:
        p.join()
