from hikyuu.interactive import *


def create_database(connect):
    sql = """
CREATE DATABASE IF NOT EXISTS hku_data;
CREATE table if not exists hku_data.day_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY market
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date)
SETTINGS index_granularity = 2048;
CREATE table if not exists hku_data.week_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY market
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.month_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY market
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.quarter_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY market
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.halfyear_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY market
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.year_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY market
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.min_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY (market, toYear(date)-toYear(date)%10)
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.min5_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY (market, toYear(date)-toYear(date)%10)
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.min15_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY (market, toYear(date)-toYear(date)%10)
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.min30_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY (market, toYear(date)-toYear(date)%10)
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.min60_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY market
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.hour2_k (
    `market` String,
    `code` String,
    `date` DateTime,
    `open` DOUBLE,
    `high` DOUBLE,
    `low` DOUBLE,
    `close` DOUBLE,
    `amount` DOUBLE,
    `volume` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY market
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.timeline (
    `market` String,
    `code` String,
    `date` DateTime,
    `price` DOUBLE,
    `vol` DOUBLE
) 
ENGINE = MergeTree()
PARTITION BY (market, toYear(date)-toYear(date)%10)
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);
CREATE table if not exists hku_data.transdata (
    `market` String,
    `code` String,
    `date` DateTime,
    `price` DOUBLE,
    `vol` DOUBLE,
    `buyorsell` int
) 
ENGINE = MergeTree()
PARTITION BY (market, toYear(date))
PRIMARY KEY (market, code, date)
ORDER BY (market, code, date);    
    """
    connect.command(sql)


def get_table(connect, market, code, ktype):
    ktype_dict = {
        'day': 'day',
        'week': 'week',
        'month': 'month',
        'quarter': 'quarter',
        'halfyear': 'halfyear',
        'year': 'year',
        'min': 'min',
        '1min': 'min',
        '5min': 'min5',
        '15min': 'min15',
        '30min': 'min30',
        '60min': 'min60',
        'hour2': 'hour2',
        'min1': 'min',
        'min5': 'min5',
        'min15': 'min15',
        'min30': 'min30',
        'min60': 'min60',
        'timeline': 'timeline',
        'transdata': 'transdata',
    }
    nktype = ktype_dict[ktype.lower()]
    if nktype == 'timeline':
        return (f'hku_data.timeline', market.upper(), code.upper())
    elif nktype == 'transdata':
        return (f'hku_data.transdata', market.upper(), code.upper())
    else:
        return (f'hku_data.{nktype}_k', market.upper(), code.upper())


@hku_catch(ret=Datetime(1970, 1, 1))
def get_lastdatetime(connect, tablename):
    tmp = connect.command("select max(date) from {} where market='{}' and code='{}'".format(
        tablename[0], tablename[1], tablename[2]))
    tmp = Datetime(tmp)
    return None if tmp == Datetime(1970, 1, 1) else tmp + UTCOffset()


@spend_time
def kdata_to_clickhouse(conn, stock, ktype, increment=True):
    market = stock.market
    code = stock.code
    table_name = get_table(conn, market, code, ktype)
    if increment:
        last_date = get_lastdatetime(conn, table_name)
        if last_date is None:
            kdata = stock.get_kdata(Query(0, ktype=ktype))
        else:
            kdata = stock.get_kdata(
                Query(last_date+Minutes(Query.get_ktype_in_min(ktype)), ktype=ktype))
    else:
        kdata = stock.get_kdata(Query(0, ktype=ktype))

    print(f"{stock.market_code} {stock.name}: {table_name} 记录数: {len(kdata)}")
    if len(kdata) == 0:
        return

    buf = []
    for k in kdata:
        buf.append([table_name[1], table_name[2], k.datetime.timestamp_utc(
        )//1000000, k.open, k.high, k.low, k.close, k.amount, k.volume])

    ic = conn.create_insert_context(table=table_name[0], database='hku_data',
                                    column_names=[
        'market', 'code', 'date', 'open', 'high', 'low', 'close', 'amount', 'volume'],
        data=buf)
    conn.insert(context=ic)


@spend_time
def timeline_to_clickhouse(conn, stock, increment=True):
    market = stock.market
    code = stock.code
    table_name = get_table(conn, market, code, 'timeline')
    if increment:
        last_date = get_lastdatetime(conn, table_name)
        if last_date is None:
            data = stock.get_timeline_list(Query(0))
        else:
            data = stock.get_timeline_list(
                Query(last_date+Seconds(1)))
    else:
        data = stock.get_timeline_list(Query(0))

    print(f"{stock.market_code} {stock.name}: {table_name} 记录数: {len(data)}")
    if len(data) == 0:
        return

    buf = []
    for d in enumerate(data):
        buf.append([table_name[1], table_name[2],
                   d.date.timestamp_utc()//1000000, d.price, d.vol])
    ic = conn.create_insert_context(table=table_name[0], data=buf)
    conn.insert(context=ic)


@spend_time
def trans_to_clickhouse(conn, stock, increment=True):
    market = stock.market
    code = stock.code
    table_name = get_table(conn, market, code, 'transdata')
    if increment:
        last_date = get_lastdatetime(conn, table_name)
        if last_date is None:
            data = stock.get_trans_list(Query(0))
        else:
            data = stock.get_trans_list(
                Query(last_date+Seconds(1)))
    else:
        data = stock.get_trans_list(Query(0))

    print(f"{stock.market_code} {stock.name}: {table_name} 记录数: {len(data)}")
    if len(data) == 0:
        return

    buf = []
    for d in data:
        buf.append([table_name[1], table_name[2],
                   d.date.timestamp_utc()//1000000, d.price, d.vol, int(d.direct)])
    ic = conn.create_insert_context(table=table_name[0], data=buf)
    conn.insert(context=ic)


def data_to_db(conn, stock, ktype, increment=True):
    nktype = ktype.lower()
    if nktype == 'timeline':
        timeline_to_clickhouse(conn, stock, increment)
    elif nktype == 'transdata':
        trans_to_clickhouse(conn, stock, increment)
    else:
        kdata_to_clickhouse(conn, stock, ktype, increment)


if __name__ == "__main__":
    from configparser import ConfigParser
    dev_config = ConfigParser()
    dev_config.read(os.path.expanduser("~") + '/workspace/dev.ini')
    db = 'clickhouse54-http'
    user = dev_config.get(db, 'user')
    password = dev_config.get(db, 'pwd')
    host = dev_config.get(db, 'host')
    port = dev_config.getint(db, 'port')

    import clickhouse_connect
    client = clickhouse_connect.get_client(
        host=host, username=user, password=password)
    create_database(client)

    # ktypes = ['timeline', "transdata", Query.DAY, Query.WEEK, Query.MONTH,
    #           Query.QUARTER, Query.HALFYEAR, Query.YEAR,
    #           Query.MIN, Query.MIN5, Query.MIN15, Query.MIN30, Query.MIN60]
    # ktypes = [Query.MIN, Query.MIN5, Query.MIN15, Query.MIN30, Query.MIN60]
    # # ktypes = [Query.MIN5,
    # #           Query.MIN15, Query.MIN30, Query.MIN60, Query.MIN]
    # # ktypes = [Query.DAY, Query.WEEK, Query.MONTH,
    # #           Query.QUARTER, Query.HALFYEAR, Query.YEAR]
    # ktypes = [Query.MIN15, Query.MIN30, Query.MIN60]
    # ktypes = [Query.WEEK, Query.MONTH,
    #           Query.QUARTER, Query.HALFYEAR, Query.YEAR]

    ktypes = [Query.WEEK]
    # stks = [s for s in sm if s.market in ('SH', )]
    stks = [s for s in sm]

    total = len(stks) * len(ktypes)
    count = 0
    for ktype in ktypes:
        for stk in stks:
            data_to_db(client, stk, ktype, True)
            count += 1
            print(
                f"====================> total: {total}, finished: {count}, percnet: {count/total*100:.2f}%")
    client.close()

    # # total = len(stks)
    # # count = 0
    # # for stk in stks:
    # #     for ktype in ktypes:
    # #         data_to_taos(connect, stk, ktype, True)
    # #     count += 1
    # #     print(
    # #         f"====================> total: {total}, finished: {count}, percnet: {count/total*100:.2f}%")

    # connect.commit()
    # connect.close()
