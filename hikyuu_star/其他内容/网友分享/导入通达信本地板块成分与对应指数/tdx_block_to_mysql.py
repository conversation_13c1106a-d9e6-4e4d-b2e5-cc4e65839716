import json
import akshare  # 这里必须再导入 akshare 下，否则get_all_zsbk_info会报错
from hikyuu.data.common import MARKET, get_stk_code_name_list
from hikyuu.util import *
from hikyuu.fetcher.stock.zh_block_tdx import (
    get_all_hybk_info,
    get_all_gnbk_info,
    get_all_fgbk_info,
    get_all_zsbk_info,
    get_all_dqbk_info,
    get_hy_category,
    get_bk_info,
)


def tdx_import_block_to_mysql(connect, code_market_dict,tdx_path, categorys=('行业板块', '概念板块', '地域板块', '指数板块','风格板块','研究行业')):

    if '行业板块' in categorys:
        hy_info = get_all_hybk_info(code_market_dict, tdx_path, hy_type='TDXHY')

    if '概念板块' in categorys:
        gn_info = get_all_gnbk_info(code_market_dict, tdx_path)

    if '地域板块' in categorys:
        dq_info = get_all_dqbk_info(code_market_dict, tdx_path)

    if '风格板块' in categorys:
        fg_info = get_all_fgbk_info(code_market_dict, tdx_path)

    if '研究行业' in categorys:
        yj_info = get_all_hybk_info(code_market_dict, tdx_path,hy_type="TDXNHY")

    if '指数板块' in categorys:
        zs_info = get_all_zsbk_info(connect,code_market_dict, tdx_path)

    block_data = hy_info + gn_info + dq_info + zs_info + fg_info + yj_info


    hku_info("更新数据库")
    cur = connect.cursor()
    # sql = "delete from hku_base.block where category in ('行业板块', '概念板块', '地域板块', '指数板块')"
    sql = "TRUNCATE TABLE  hku_base.block"
    cur.execute(sql)

    if len(block_data)>0:
        sql = "insert into hku_base.block (category, name, market_code,block_code) values (%s,%s,%s,%s)"
        cur.executemany(sql, block_data)

    connect.commit()
    cur.close()



if __name__ == '__main__':
    import mysql.connector

    host = '*************'
    port = 3306
    usr = 'root'
    pwd = 'xxx1234'

    src_dir = "D:\\zd_zsone"
    quotations = ['stock', 'fund']  # 通达信盘后数据没有债券
    connect = mysql.connector.connect(user=usr, password=pwd, host=host, port=port)
    code_market_dict = {}

    for market in (MARKET.SH, MARKET.SZ, MARKET.BJ):
        x = get_stk_code_name_list(market)
        for v in x:
            code_market_dict[v["code"]] = market

    tdx_import_block_to_mysql(connect, code_market_dict, src_dir)

    connect.close()