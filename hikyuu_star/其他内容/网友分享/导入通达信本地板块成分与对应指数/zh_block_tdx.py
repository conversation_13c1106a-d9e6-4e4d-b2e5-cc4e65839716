
import requests
import akshare as ak
import pandas as pd
from hikyuu.util import *
from pytdx.reader.block_reader import BlockReader

RLPATH = '/T0002/hq_cache'
col_order = ["category","name","market_code","block_code"]
category_name = {"gn": "概念板块", "fg": "风格板块", "zs": "指数板块","dq":"地区板块"}

def print_droped_info(origin_df, drop_df):

    delete_rows = origin_df[~origin_df.index.isin(drop_df.index)]
    print("删除行业板块：\n")
    print(delete_rows)


def tdx_get_stock_industry_loc(path, hy_type='TDXHY'):
    """
    从本地文件中获取所有行业的股票。
    tdxhy.cfg 里有两类行业代码，一类是通达信行业代码，一类是研究行业代码
    :return:
    """

    hy_code_file_path = path + '/incon.dat'
    hy_file_path = path + RLPATH + '/tdxhy.cfg'

    with open(hy_code_file_path, encoding='GB18030', mode='r') as f:
        incon_list = f.readlines()
        incon_dict = {}

        for i in incon_list:

            if not len(i.strip()) or ('#' * 3 in i):
                continue

            if i[0] == '#' and i[1] != '#':
                j = i.replace('#', '').replace('\n', '')
                incon_dict[j] = []
            else:
                incon_dict[j].append(i.replace('\n', '').split('|'))

    incon = pd.concat([pd.DataFrame(v).assign(type=k) for k, v in incon_dict.items()]) \
        .rename({0: 'code', 1: 'name'}, axis=1).reset_index(drop=True)

    with open(hy_file_path, encoding='GB18030', mode='r') as f:
        hy = f.readlines()

    hy = [line.replace('\n', '') for line in hy]
    hy = pd.DataFrame(line.split('|') for line in hy)

    hy = hy[~hy[1].str.startswith('9')]
    hy = hy[~hy[1].str.startswith('2')]

    hy1 = hy[[1, 2]].set_index(2).join(incon.set_index('code')).set_index(1)[['name', 'type']]
    hy2 = hy[[1, 5]].set_index(5).join(incon.set_index('code')).set_index(1)[['name', 'type']]

    df = hy.set_index(1) \
        .join(
        hy1.rename({'name': hy1.dropna()['type'].values[0], 'type': hy1.dropna()['type'].values[0] + '_type'}, axis=1)) \
        .join(hy2.rename({'name': hy2.dropna()['type'].values[0], 'type': hy2.dropna()['type'].values[0] + '_type'},
                         axis=1)).reset_index()
    df.rename({0: 'sse', 1: 'code', 2: 'TDX_code', 5: 'SW_code', 'TDXRSHY': 'swhy'}, axis=1, inplace=True)
    df = df[[i for i in df.columns if not isinstance(i, int) and '_type' not in str(i)]]
    df.columns = [i.lower() for i in df.columns]

    # 这里返回通达信正常的行业数据，删除研究行业数据
    if hy_type== "TDXHY":
        df.drop(['sw_code', 'swhy'], axis=1, inplace=True)
        df.rename(columns={'tdx_code': 'hy_code','tdxnhy':'hy_name'}, inplace=True)
    elif hy_type == "TDXNHY":
        df.drop(['tdx_code', 'tdxnhy'], axis=1, inplace=True)
        df.rename(columns={'sw_code': 'hy_code', 'swhy': 'hy_name'}, inplace=True)

    return df


def read_file_loc(file_name, splits):
    with open(file_name, 'r') as f:
        buf_lis = f.read().split('\n')
    return [x.split(splits) for x in buf_lis[:-1]]


def get_hy_category(path, block='hy'):
    # 基于tdxzs3拿到行业分类
    buf_line = read_file_loc(path +RLPATH +'/tdxzs3.cfg', '|')
    mapping = {'hy': '2', 'dq': '3', 'gn': '4', 'fg': '5', 'yjhy': '12', 'zs': '6'}
    df = pd.DataFrame(buf_line, columns=['name', 'code', 'type', 't1', 't2', 'block'])
    dg = df.groupby(by='type')
    temp = dg.get_group(mapping[block]).reset_index(drop=True)
    temp.drop(temp.columns[[2, 3, 4]], axis=1, inplace=True)
    temp.rename(columns={ 'code': 'block_code'}, inplace=True)
    return temp



@hku_catch(ret={}, trace=True)
def get_all_hybk_info(code_market_dict, path, hy_type='TDXHY'):
    """获取行业板块或研究行业板块"""
    hy_info = []
    if hy_type == "TDXHY":
        block = 'hy'
    elif hy_type == "TDXNHY":
        block = 'yjhy'

    #拿到通达信行业的股票信息
    stocks_of_hy = tdx_get_stock_industry_loc(path, hy_type)
    df_hy = get_hy_category(path, block)

    if hy_type == "TDXHY":
        # 只取一级行业
        df_hy = df_hy[df_hy.block.str.len()==5]
        stocks_of_hy['hy_code']=stocks_of_hy['hy_code'].str[:5]

    # 合并
    df_stock_hy = pd.merge(stocks_of_hy, df_hy, left_on='hy_code',right_on='block' ,how='left')
    df_stock_hy['code'] = df_stock_hy['code'].apply(lambda x: code_market_dict[x].upper()+x  if x in code_market_dict else None)

    df_stock_hy = df_stock_hy.drop(columns=["sse","hy_code",'hy_name','block']).rename(columns={'code':'market_code'})

    if hy_type == "TDXHY":
        df_stock_hy['category'] ="行业板块"
    else:
        df_stock_hy['category'] = "研究行业"
    # 排序
    df_stock_hy = df_stock_hy[col_order]

    df_stock_hy.dropna(inplace=True)

    for  index , row  in df_stock_hy.iterrows():
        hy_info.append(tuple(row))

    return hy_info



def  get_bk_info(code_market_dict, path, block='gn'):
    """
    获取 概念、风格、指数信息的公共函数。
    :param code_market_dict:
    :param path:
    :return:
    """
    bk_info =  []

    block_file ={"gn":"/block_gn.dat","fg":"/block_fg.dat","zs":"/block_zs.dat"}

    file = block_file[block]
    file_name = path+RLPATH + file
    df_stock= BlockReader().get_df(file_name)
    df_block = get_hy_category(path, block)
    df_stock['code']=df_stock['code'].apply(lambda x :  code_market_dict[x].upper()+x if x in code_market_dict else None)
    df_stock = df_stock.drop(columns=["block_type","code_index"])
    df_block = df_block.drop(columns=["name"])

    # 合并
    df_stock = pd.merge(df_stock, df_block, left_on='blockname', right_on='block',how='left')
    df_stock.rename(columns={'block':'name'}, inplace=True)


    df_stock.drop(columns=["blockname"], inplace=True)
    df_stock.rename(columns={'code':'market_code'}, inplace=True)
    df_stock['category'] = category_name[block]
    df_stock = df_stock[col_order]

    df_stock.dropna(inplace=True)
    # if 1:
    #     print_droped_info(df_stock, dropped_df)

    for index, row in df_stock.iterrows():
        bk_info.append(tuple(row))

    return bk_info
def get_all_gnbk_info(code_market_dict, path):
    """获取概念板块"""

    return get_bk_info(code_market_dict, path, block='gn')

@hku_catch(ret={}, trace=True)
def get_all_fgbk_info(code_market_dict, path):
    """获取风格板块"""
    return get_bk_info(code_market_dict, path, block='fg')


@hku_catch(ret={}, trace=True)
def get_all_zsbk_info( connect , code_market_dict, path):
    """
    指数获取不能跟概念类似的方式获取，tdxzs3.cfg 这个文件没有指数分类，
    所以我直接从数据库中拿吧
    :param code_market_dict:
    :param path:
    :return:
    """
    hku_info("开始获取指数板块信息")
    zs_info =[]
    file = path+RLPATH + "/block_zs.dat"
    try:
        cur =connect.cursor()
        sql = "select code , name  from hku_base.stock where type=2"
        cur.execute(sql)
        blk_list = cur.fetchall()
        cur.close()
    except Exception as e:
        cur.close()
        print(e)
        return []

    if len(blk_list)<=0:
        hku_error("获取指数信息失败")
        return []

    df_blk = pd.DataFrame(blk_list, columns=["block_code", "name"])
    df_stock = BlockReader().get_df(file)

    df_stock = df_stock.drop(columns=["block_type","code_index"])

    df_stock = pd.merge(df_stock, df_blk, left_on='blockname', right_on='name', how='left')
    df_stock.drop(columns=["blockname"], inplace=True)
    df_stock.rename(columns={'code':'market_code'}, inplace=True)
    df_stock['category'] = category_name["zs"]
    df_stock = df_stock[col_order]

    df_stock.dropna(inplace=True)

    for index, row in df_stock.iterrows():
        zs_info.append(tuple(row))

    return zs_info


@hku_catch(ret={}, trace=True)
def get_all_dqbk_info(code_market_dict,path):

    dq_info= []
    df_stock = pd.DataFrame(columns=["blockname","market_code"])
    """获取地域板块"""
    url = "http://13.push2.eastmoney.com/api/qt/clist/get"
    params = {
        "pn": "1",
        "pz": "50000",
        "po": "1",
        "np": "1",
        "ut": "bd1d9ddb04089700cf9c27f6f7426281",
        "fltt": "2",
        "invt": "2",
        "fid": "f3",
        "fs": "m:90+t:1+f:!50",
        "fields": "f12,f14",
        # "fields": "f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,"
        #           "f136,f115,f152",
        "_": "1623833739532",
    }
    r = requests.get(url, params=params)
    data_json = r.json()
    data_json = data_json["data"]["diff"]

    params = {
        "pn": "1",
        "pz": "2000",
        "po": "1",
        "np": "1",
        "ut": "bd1d9ddb04089700cf9c27f6f7426281",
        "fltt": "2",
        "invt": "2",
        "fid": "f3",
        # "fs": f"b:{stock_board_code} f:!50",
        "fields": "f12",
        "_": "1626081702127",
    }

    ret = {}
    for v in data_json:
        # print(v)
        block_stocks = {}

        blk_code = v["f12"]
        blk_name = v["f14"]
        block_stocks["blockname"]=[]

        if blk_name == "内蒙古":
            blk_name = "内蒙板块"

        block_stocks["blockname"].append(blk_name)
        block_stocks["market_code"] = []

        # print(blk_name)
        params["fs"] = f"b:{blk_code} f:!50"
        r = requests.get(url, params=params)
        stk_json = r.json()
        stk_json = stk_json["data"]["diff"]

        for item in stk_json:
            stk_code = item["f12"]
            try:
                block_stocks["market_code"].append(f"{code_market_dict[stk_code].upper()}{stk_code}")
            except:
                # print(stk_code)
                pass

        block_stocks["blockname"] = block_stocks["blockname"] * len(block_stocks["market_code"])

        tmp_df = pd.DataFrame(block_stocks)
        df_stock = pd.concat([df_stock, tmp_df], ignore_index=True)

    # 获取地域板块
    df_block = get_hy_category(path, block='dq')
    df_stock = pd.merge(df_stock, df_block, left_on='blockname', right_on='name', how='left')
    df_stock["category"] = category_name["dq"]
    dropped_df = df_stock.dropna()
    if 1 :
        print_droped_info(df_stock, dropped_df)

    df_stock.drop(columns=["blockname"], inplace=True)
    df_stock = df_stock[col_order]

    for index, row in df_stock.iterrows():
        dq_info.append(tuple(row))

    return dq_info



if __name__ == '__main__':

    dq_bk = get_all_dqbk_info({}, "")