


import datetime
from datetime import datetime, timedelta
import requests
from py_mini_racer import MiniRacer
import akshare as ak
from akshare.stock.cons import hk_js_decode
import pymysql
from sqlalchemy import create_engine
import pandas as pd

# MySQL数据库配置
DB_CONFIG = {
    'host': '***********',
    'user': 'root',
    'password': 'rootroot',
    'database': 'hku_base',
    'port': 3306
}



def create_date_dataframe(start_date, end_date):
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")
    
    date_list = []
    current_date = start
    while current_date <= end:
        date_list.append(current_date.strftime("%Y-%m-%d"))
        current_date += timedelta(days=1)
    
    df = pd.DataFrame(date_list, columns=['date'])
    return df

'''

TRUNCATE TABLE  hku_base.holiday;
insert into hku_base.holiday(`date`) select CAST(DATE_FORMAT(df.`date`, '%Y%m%d') AS UNSIGNED) from date_full df where `date` not in (select date from trade_date ) and DAYOFWEEK(df.`date` ) NOT IN (1,7);

'''



def save_to_mysql(dataframe, table_name):
    """使用SQLAlchemy保存数据到MySQL"""
    try:
        engine = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset=utf8mb4"
        )
        dataframe.to_sql(name=table_name, con=engine, if_exists='replace', index=False)
        print(f"数据成功保存到MySQL表{table_name}")
    except Exception as e:
        print("保存数据到MySQL失败:", e)


def flush_holiday_table():
    # 建立数据库连接
    conn = pymysql.connect(
        host=DB_CONFIG['host'],
        user=DB_CONFIG['user'],
        password=DB_CONFIG['password'],
        database=DB_CONFIG['database'],
        charset='utf8mb4'
    )
    
    try:
        with conn.cursor() as cursor:
            # TRUNCATE操作
            cursor.execute("TRUNCATE TABLE  hku_base.holiday")
            
            # INSERT操作
            insert_sql = "insert into hku_base.holiday(`date`) select CAST(DATE_FORMAT(df.`date`, '%Y%m%d') AS UNSIGNED) from date_full df where `date` not in (select date from trade_date ) and DAYOFWEEK(df.`date` ) NOT IN (1,7)"
            cursor.execute(insert_sql)
            
        # 提交事务
        conn.commit()
        print("操作成功完成")
        
    except Exception as e:
        conn.rollback()
        print(f"操作失败: {e}")
    finally:
        conn.close()



if __name__ == '__main__':
    # 1. 获取股票数据
    
    # 使用示例
    start_date = "1990-12-19"
    end_date = "2025-12-31"
    date_df = create_date_dataframe(start_date, end_date)

    if date_df is not None:
        # 2. 数据预处理
        date_df.columns = [ 'date']  # 统一列名
        
        # 3. 保存到MySQL
        save_to_mysql(date_df, 'date_full')
        flush_holiday_table();