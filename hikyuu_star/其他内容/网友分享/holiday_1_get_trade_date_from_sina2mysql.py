




import datetime
import requests
from py_mini_racer import MiniRacer
import akshare as ak
from akshare.stock.cons import hk_js_decode
import pymysql
from sqlalchemy import create_engine
import pandas as pd

# MySQL数据库配置
DB_CONFIG = {
    'host': '***********',
    'user': 'root',
    'password': 'rootroot',
    'database': 'hku_base',
    'port': 3306
}


def get_trade_date_sina() -> pd.DataFrame:
    """
    新浪财经-交易日历-历史数据
    https://finance.sina.com.cn/realstock/company/klc_td_sh.txt
    :return: 交易日历
    :rtype: pandas.DataFrame
    """
    url = "https://finance.sina.com.cn/realstock/company/klc_td_sh.txt"
    r = requests.get(url)
    js_code = MiniRacer()
    js_code.eval(hk_js_decode)
    dict_list = js_code.call("d", r.text.split("=")[1].split(";")[0].replace('"', ""))
    print(dict_list)
    temp_df = pd.DataFrame(dict_list)
    temp_df.columns = ["trade_date"]
    temp_df["trade_date"] = pd.to_datetime(temp_df["trade_date"]).dt.date
    temp_list = temp_df["trade_date"].to_list()
    # 该日期是交易日，但是在新浪返回的交易日历缺失该日期，这里补充上
    temp_list.append(datetime.date(year=1992, month=5, day=4))
    temp_list.sort()
    temp_df = pd.DataFrame(temp_list, columns=["date"])
    temp_df.to_csv('c:\\out.csv', index=False)
    return temp_df



def save_to_mysql(dataframe, table_name):
    """使用SQLAlchemy保存数据到MySQL"""
    try:
        engine = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset=utf8mb4"
        )
        dataframe.to_sql(name=table_name, con=engine, if_exists='replace', index=False)
        print(f"数据成功保存到MySQL表{table_name}")
    except Exception as e:
        print("保存数据到MySQL失败:", e)

if __name__ == '__main__':
    # 1. 获取股票数据
    trade_date_df = get_trade_date_sina()
    
    if trade_date_df is not None:
        # 2. 数据预处理
        trade_date_df.columns = [ 'date']  # 统一列名
        
        # 3. 保存到MySQL
        save_to_mysql(trade_date_df, 'trade_date')
