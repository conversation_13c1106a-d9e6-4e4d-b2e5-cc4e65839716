#!/usr/bin/python
# -*- coding: utf8 -*-
#
# Create on: 2024-8-17
#    Author: <PERSON><PERSON><PERSON>

#
# 用于修复 mysql 表。
#
#

import datetime
import mysql.connector
from mysql.connector import Error

from hikyuu.util import *


# 配置连接池，按需修改如下参数
dbconfig = {
    "host": "127.0.0.1",
    "port": 3306,
    "user": "root",
    "password": 'root',
    "buffered": True,
}

cnxpool = mysql.connector.pooling.MySQLConnectionPool(
    pool_name="mypool", pool_size=10, **dbconfig)


def get_conn():
    # 从连接池中获取一个连接
    conn = cnxpool.get_connection()
    cursor = conn.cursor()
    return conn, cursor


def close_conn(conn, cursor):
    # 关闭连接和游标，但连接会放回连接池
    cursor.close()
    conn.close()


@hku_catch(trace=True)
def get_all_tables(market, typ):
    # 使用连接池
    conn, cursor = get_conn()
    try:
        sql = f"SELECT table_schema, table_name FROM information_schema.tables WHERE table_schema='{market}_{typ}'"
        print(sql)
        cursor.execute(sql)
        x = cursor.fetchall()
        conn.commit()
        return [f'`{v[0]}`.`{v[1]}`' for v in x]
    except Error as e:
        error_message = str(e).split('\n')[0]
        print(f"失败: {error_message}")
        conn.rollback()
    except Exception as e:
        print(f"失败 is exception: {e}")

        conn.rollback()
    finally:
        close_conn(conn, cursor)


def repair_tables(market, typ):
    tables = get_all_tables(market, typ)
    for table in tables:
        hku_info(f"repair table {table}")
        sql = f"repair table {table};"
        conn, cursor = get_conn()
        try:
            print(sql)
            cursor.execute(sql)
            print(f"表 {table} 已修复。")
            conn.commit()
        except Error as e:
            # 如果遇到错误，则尝试获取更多的错误信息
            error_message = str(e).split('\n')[0]
            if 'have no tables' in error_message:
                print(f"表 {table} 不存在。")
                conn.rollback()
            elif 'No data to repair' in error_message:
                print(f"表 {table} 无需修复。")
                conn.rollback()
            else:
                print(f"表 {table} 修复失败: {error_message}")
                conn.rollback()
        finally:
            close_conn(conn, cursor)


if __name__ == '__main__':

    market_list = ['sh', 'sz', 'bj']
    typ_list = ['time', 'trans', 'day', 'week', 'month', 'quarter',
                'halfyear', 'year', 'min', 'min5', 'min15', 'min30', 'min60']
    for market in market_list:
        for typ in typ_list:
            repair_tables(market, typ)
