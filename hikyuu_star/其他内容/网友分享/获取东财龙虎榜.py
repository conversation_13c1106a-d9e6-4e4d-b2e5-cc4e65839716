# -*- coding:utf-8 -*-
# !/usr/bin/env python
"""
Date: 2022/3/15 17:32
Desc: 东方财富网-数据中心-龙虎榜单
https://data.eastmoney.com/stock/tradedetail.html
"""
import aiohttp

import pandas as pd

from tqdm import tqdm

timeout = aiohttp.ClientTimeout(
    connect=30,  # 连接超时时间为5秒
    sock_read=60  # 读取超时时间为10秒
)
import datetime
def get_now_year_month_day():
    d=datetime.datetime.now()
    return f"{d.year}{d.month:02}{d.day:02}"
def get_lastday_year_month_day(n,fmt="%Y%m%d"):
    from datetime import datetime, timedelta
    # 获取当前日期和时间
    current_date = datetime.now()
    # 计算N天后的日期
    future_date = current_date - timedelta(days=n)
    return future_date.strftime(fmt)



async def _post(url, params):
    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.post(url, json=params) as response:
            data_json = await response.json(content_type=None)
            return data_json

async def _get(url,params):
    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.get(url,params=params) as response:
            data_json = await response.json(content_type=None)
            return data_json


async def aio_stock_lhb_detail_em_today(
) -> pd.DataFrame:
    return await aio_stock_lhb_detail_em(start_date=get_now_year_month_day())


async def aio_stock_lhb_detail_em_lastday(lastday=1
) -> pd.DataFrame:
    
    start_date= get_lastday_year_month_day(lastday)
    return await aio_stock_lhb_detail_em(start_date=start_date)

async def aio_stock_lhb_detail_em_oneday(lastday=1
) -> pd.DataFrame:
    
    start_date= get_lastday_year_month_day(lastday)
    return await aio_stock_lhb_detail_em(start_date=start_date,end_date=start_date)


async def aio_stock_lhb_detail_em(
    start_date: str = "20230403", end_date: str = get_now_year_month_day()
) -> pd.DataFrame:
    """
    东方财富网-数据中心-龙虎榜单-龙虎榜详情
    https://data.eastmoney.com/stock/tradedetail.html
    :param start_date: 开始日期
    :type start_date: str
    :param end_date: 结束日期
    :type end_date: str
    :return: 龙虎榜详情
    :rtype: pandas.DataFrame
    """
    start_date = "-".join([start_date[:4], start_date[4:6], start_date[6:]])
    end_date = "-".join([end_date[:4], end_date[4:6], end_date[6:]])
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
    params = {
        "sortColumns": "SECURITY_CODE,TRADE_DATE",
        "sortTypes": "1,-1",
        "pageSize": "5000",
        "pageNumber": "1",
        "reportName": "RPT_DAILYBILLBOARD_DETAILSNEW",
        "columns": "SECURITY_CODE,SECUCODE,SECURITY_NAME_ABBR,TRADE_DATE,EXPLAIN,CLOSE_PRICE,CHANGE_RATE,BILLBOARD_NET_AMT,BILLBOARD_BUY_AMT,BILLBOARD_SELL_AMT,BILLBOARD_DEAL_AMT,ACCUM_AMOUNT,DEAL_NET_RATIO,DEAL_AMOUNT_RATIO,TURNOVERRATE,FREE_MARKET_CAP,EXPLANATION,D1_CLOSE_ADJCHRATE,D2_CLOSE_ADJCHRATE,D5_CLOSE_ADJCHRATE,D10_CLOSE_ADJCHRATE,SECURITY_TYPE_CODE",
        "source": "WEB",
        "client": "WEB",
        "filter": f"(TRADE_DATE<='{end_date}')(TRADE_DATE>='{start_date}')",
    }
    data_json = await _get(url, params)

    total_page = data_json["result"]["pages"]
    big_df = pd.DataFrame()
    if False:
        for page in range(1, total_page + 1):
            params.update(
                {
                    "pageNumber": page,
                }
            )
            data_json = await _get(url, params)
            temp_df = pd.DataFrame(data_json["result"]["data"])
            big_df = pd.concat([big_df, temp_df], ignore_index=True)

    def __update(page):
        params.update({"pageNumber": page})
        return dict(params)

    # 创建任务列表，将每个请求封装成一个任务
    tasks = [_get(url,__update( page)) for page in range(1, total_page + 1)]
    # 等待所有任务完成，获取结果
    responses = await asyncio.gather(*tasks, return_exceptions=True)
    # 解析每个响应，并合并结果
    for data_json in responses:
        temp_df = pd.DataFrame(data_json["result"]["data"])
        big_df = pd.concat([big_df, temp_df], ignore_index=True)



    big_df.reset_index(inplace=True)
    big_df["index"] = big_df.index + 1
    big_df.rename(
        columns={
            "index": "序号",
            "SECURITY_CODE": "代码",
            "SECUCODE": "-",
            "SECURITY_NAME_ABBR": "名称",
            "TRADE_DATE": "上榜日",
            "EXPLAIN": "解读",
            "CLOSE_PRICE": "收盘价",
            "CHANGE_RATE": "涨跌幅",
            "BILLBOARD_NET_AMT": "龙虎榜净买额",
            "BILLBOARD_BUY_AMT": "龙虎榜买入额",
            "BILLBOARD_SELL_AMT": "龙虎榜卖出额",
            "BILLBOARD_DEAL_AMT": "龙虎榜成交额",
            "ACCUM_AMOUNT": "市场总成交额",
            "DEAL_NET_RATIO": "净买额占总成交比",
            "DEAL_AMOUNT_RATIO": "成交额占总成交比",
            "TURNOVERRATE": "换手率",
            "FREE_MARKET_CAP": "流通市值",
            "EXPLANATION": "上榜原因",
            "D1_CLOSE_ADJCHRATE": "上榜后1日",
            "D2_CLOSE_ADJCHRATE": "上榜后2日",
            "D5_CLOSE_ADJCHRATE": "上榜后5日",
            "D10_CLOSE_ADJCHRATE": "上榜后10日",
        },
        inplace=True,
    )

    big_df = big_df[
        [
            "序号",
            "代码",
            "名称",
            "上榜日",
            "解读",
            "收盘价",
            "涨跌幅",
            "龙虎榜净买额",
            "龙虎榜买入额",
            "龙虎榜卖出额",
            "龙虎榜成交额",
            "市场总成交额",
            "净买额占总成交比",
            "成交额占总成交比",
            "换手率",
            "流通市值",
            "上榜原因",
            "上榜后1日",
            "上榜后2日",
            "上榜后5日",
            "上榜后10日",
        ]
    ]
    big_df["上榜日"] = pd.to_datetime(big_df["上榜日"]).dt.date

    big_df["收盘价"] = pd.to_numeric(big_df["收盘价"], errors="coerce")
    big_df["涨跌幅"] = pd.to_numeric(big_df["涨跌幅"], errors="coerce")
    big_df["龙虎榜净买额"] = pd.to_numeric(big_df["龙虎榜净买额"], errors="coerce")
    big_df["龙虎榜买入额"] = pd.to_numeric(big_df["龙虎榜买入额"], errors="coerce")
    big_df["龙虎榜卖出额"] = pd.to_numeric(big_df["龙虎榜卖出额"], errors="coerce")
    big_df["龙虎榜成交额"] = pd.to_numeric(big_df["龙虎榜成交额"], errors="coerce")
    big_df["市场总成交额"] = pd.to_numeric(big_df["市场总成交额"], errors="coerce")
    big_df["净买额占总成交比"] = pd.to_numeric(big_df["净买额占总成交比"], errors="coerce")
    big_df["成交额占总成交比"] = pd.to_numeric(big_df["成交额占总成交比"], errors="coerce")
    big_df["换手率"] = pd.to_numeric(big_df["换手率"], errors="coerce")
    big_df["流通市值"] = pd.to_numeric(big_df["流通市值"], errors="coerce")
    big_df["上榜后1日"] = pd.to_numeric(big_df["上榜后1日"], errors="coerce")
    big_df["上榜后2日"] = pd.to_numeric(big_df["上榜后2日"], errors="coerce")
    big_df["上榜后5日"] = pd.to_numeric(big_df["上榜后5日"], errors="coerce")
    big_df["上榜后10日"] = pd.to_numeric(big_df["上榜后10日"], errors="coerce")
    return big_df


async def aio_stock_lhb_stock_statistic_em(symbol: str = "近一月") -> pd.DataFrame:
    """
    东方财富网-数据中心-龙虎榜单-个股上榜统计
    https://data.eastmoney.com/stock/tradedetail.html
    :param symbol: choice of {"近一月", "近三月", "近六月", "近一年"}
    :type symbol: str
    :return: 个股上榜统计
    :rtype: pandas.DataFrame
    """
    symbol_map = {
        "近一月": "01",
        "近三月": "02",
        "近六月": "03",
        "近一年": "04",
    }
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
    params = {
        "sortColumns": "BILLBOARD_TIMES,LATEST_TDATE,SECURITY_CODE",
        "sortTypes": "-1,-1,1",
        "pageSize": "500",
        "pageNumber": "1",
        "reportName": "RPT_BILLBOARD_TRADEALL",
        "columns": "ALL",
        "source": "WEB",
        "client": "WEB",
        "filter": f'(STATISTICS_CYCLE="{symbol_map[symbol]}")',
    }
    data_json = await _get(url, params)

    temp_df = pd.DataFrame(data_json["result"]["data"])
    temp_df.reset_index(inplace=True)
    temp_df["index"] = temp_df.index + 1
    temp_df.columns = [
        "序号",
        "-",
        "代码",
        "最近上榜日",
        "名称",
        "近1个月涨跌幅",
        "近3个月涨跌幅",
        "近6个月涨跌幅",
        "近1年涨跌幅",
        "涨跌幅",
        "收盘价",
        "-",
        "龙虎榜总成交额",
        "龙虎榜净买额",
        "-",
        "-",
        "机构买入净额",
        "上榜次数",
        "龙虎榜买入额",
        "龙虎榜卖出额",
        "机构买入总额",
        "机构卖出总额",
        "买方机构次数",
        "卖方机构次数",
        "-",
    ]
    temp_df = temp_df[
        [
            "序号",
            "代码",
            "名称",
            "最近上榜日",
            "收盘价",
            "涨跌幅",
            "上榜次数",
            "龙虎榜净买额",
            "龙虎榜买入额",
            "龙虎榜卖出额",
            "龙虎榜总成交额",
            "买方机构次数",
            "卖方机构次数",
            "机构买入净额",
            "机构买入总额",
            "机构卖出总额",
            "近1个月涨跌幅",
            "近3个月涨跌幅",
            "近6个月涨跌幅",
            "近1年涨跌幅",
        ]
    ]
    temp_df["最近上榜日"] = pd.to_datetime(temp_df["最近上榜日"]).dt.date
    return temp_df

async def aio_stock_lhb_jgmmtj_em_today(
) -> pd.DataFrame:
    return await aio_stock_lhb_jgmmtj_em(start_date=get_now_year_month_day())


async def aio_stock_lhb_jgmmtj_em_lastday(lastday=1
) -> pd.DataFrame:
    
    start_date= get_lastday_year_month_day(lastday)
    return await aio_stock_lhb_jgmmtj_em(start_date=start_date)
async def aio_aio_stock_lhb_jgmmtj_em_oneday(lastday=1
) -> pd.DataFrame:
    
    start_date= get_lastday_year_month_day(lastday)
    return await aio_stock_lhb_jgmmtj_em(start_date=start_date,end_date=start_date)


async def aio_stock_lhb_jgmmtj_em(
    start_date: str = "20220906", end_date: str = get_now_year_month_day()
) -> pd.DataFrame:
    """
    东方财富网-数据中心-龙虎榜单-机构买卖每日统计
    https://data.eastmoney.com/stock/jgmmtj.html
    :param start_date: 开始日期
    :type start_date: str
    :param end_date: 结束日期
    :type end_date: str
    :return: 机构买卖每日统计
    :rtype: pandas.DataFrame
    """
    start_date = "-".join([start_date[:4], start_date[4:6], start_date[6:]])
    end_date = "-".join([end_date[:4], end_date[4:6], end_date[6:]])
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
    params = {
        "sortColumns": "NET_BUY_AMT,TRADE_DATE,SECURITY_CODE",
        "sortTypes": "-1,-1,1",
        "pageSize": "5000",
        "pageNumber": "1",
        "reportName": "RPT_ORGANIZATION_TRADE_DETAILS",
        "columns": "ALL",
        "source": "WEB",
        "client": "WEB",
        "filter": f"(TRADE_DATE>='{start_date}')(TRADE_DATE<='{end_date}')",
    }
    data_json = await _get(url, params)
    temp_df = pd.DataFrame(data_json["result"]["data"])
    temp_df.reset_index(inplace=True)
    temp_df["index"] = temp_df.index + 1
    temp_df.columns = [
        "序号",
        "-",
        "名称",
        "代码",
        "上榜日期",
        "收盘价",
        "涨跌幅",
        "买方机构数",
        "卖方机构数",
        "机构买入总额",
        "机构卖出总额",
        "机构买入净额",
        "市场总成交额",
        "机构净买额占总成交额比",
        "换手率",
        "流通市值",
        "上榜原因",
        "-",
        "-",
        "-",
        "-",
        "-",
        "-",
        "-",
        "-",
        "-",
    ]
    temp_df = temp_df[
        [
            "序号",
            "代码",
            "名称",
            "收盘价",
            "涨跌幅",
            "买方机构数",
            "卖方机构数",
            "机构买入总额",
            "机构卖出总额",
            "机构买入净额",
            "市场总成交额",
            "机构净买额占总成交额比",
            "换手率",
            "流通市值",
            "上榜原因",
            "上榜日期",
        ]
    ]
    temp_df["上榜日期"] = pd.to_datetime(temp_df["上榜日期"]).dt.date
    temp_df["收盘价"] = pd.to_numeric(temp_df["收盘价"], errors="coerce")
    temp_df["涨跌幅"] = pd.to_numeric(temp_df["涨跌幅"], errors="coerce")
    temp_df["买方机构数"] = pd.to_numeric(temp_df["买方机构数"], errors="coerce")
    temp_df["卖方机构数"] = pd.to_numeric(temp_df["卖方机构数"], errors="coerce")
    temp_df["机构买入总额"] = pd.to_numeric(temp_df["机构买入总额"], errors="coerce")
    temp_df["机构卖出总额"] = pd.to_numeric(temp_df["机构卖出总额"], errors="coerce")
    temp_df["机构买入净额"] = pd.to_numeric(temp_df["机构买入净额"], errors="coerce")
    temp_df["市场总成交额"] = pd.to_numeric(temp_df["市场总成交额"], errors="coerce")
    temp_df["机构净买额占总成交额比"] = pd.to_numeric(temp_df["机构净买额占总成交额比"], errors="coerce")
    temp_df["换手率"] = pd.to_numeric(temp_df["换手率"], errors="coerce")
    temp_df["流通市值"] = pd.to_numeric(temp_df["流通市值"], errors="coerce")

    return temp_df


async def aio_stock_lhb_jgstatistic_em(symbol: str = "近一月") -> pd.DataFrame:
    """
    东方财富网-数据中心-龙虎榜单-机构席位追踪
    https://data.eastmoney.com/stock/jgstatistic.html
    :param symbol: choice of {"近一月", "近三月", "近六月", "近一年"}
    :type symbol: str
    :return: 机构席位追踪
    :rtype: pandas.DataFrame
    """
    symbol_map = {
        "近一月": "01",
        "近三月": "02",
        "近六月": "03",
        "近一年": "04",
    }
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
    params = {
        "sortColumns": "ONLIST_TIMES,SECURITY_CODE",
        "sortTypes": "-1,1",
        "pageSize": "5000",
        "pageNumber": "1",
        "reportName": "RPT_ORGANIZATION_SEATNEW",
        "columns": "ALL",
        "source": "WEB",
        "client": "WEB",
        "filter": f'(STATISTICSCYCLE="{symbol_map[symbol]}")',
    }
    data_json = await _get(url, params)
    total_page = data_json["result"]["pages"]
    big_df = pd.DataFrame()

    if False:
        for page in tqdm(range(1, total_page + 1), leave=False):
            params.update({"pageNumber": page})
            data_json = await _get(url, params)
            temp_df = pd.DataFrame(data_json["result"]["data"])
            big_df = pd.concat([big_df, temp_df], ignore_index=True)


    def __update(page):
        params.update({"pageNumber": page})
        return dict(params)

    # 创建任务列表，将每个请求封装成一个任务
    tasks = [_get(url,__update( page)) for page in range(1, total_page + 1)]
    # 等待所有任务完成，获取结果
    responses = await asyncio.gather(*tasks, return_exceptions=True)
    # 解析每个响应，并合并结果
    for data_json in responses:
        temp_df = pd.DataFrame(data_json["result"]["data"])
        big_df = pd.concat([big_df, temp_df], ignore_index=True)


    big_df.reset_index(inplace=True)
    big_df["index"] = big_df.index + 1
    big_df.rename(
        columns={
            "index": "序号",
            "SECURITY_CODE": "代码",
            "SECURITY_NAME_ABBR": "名称",
            "CLOSE_PRICE": "收盘价",
            "CHANGE_RATE": "涨跌幅",
            "AMOUNT": "龙虎榜成交金额",
            "ONLIST_TIMES": "上榜次数",
            "BUY_AMT": "机构买入额",
            "BUY_TIMES": "机构买入次数",
            "SELL_AMT": "机构卖出额",
            "SELL_TIMES": "机构卖出次数",
            "NET_BUY_AMT": "机构净买额",
            "M1_CLOSE_ADJCHRATE": "近1个月涨跌幅",
            "M3_CLOSE_ADJCHRATE": "近3个月涨跌幅",
            "M6_CLOSE_ADJCHRATE": "近6个月涨跌幅",
            "Y1_CLOSE_ADJCHRATE": "近1年涨跌幅",
        },
        inplace=True,
    )
    big_df = big_df[
        [
            "序号",
            "代码",
            "名称",
            "收盘价",
            "涨跌幅",
            "龙虎榜成交金额",
            "上榜次数",
            "机构买入额",
            "机构买入次数",
            "机构卖出额",
            "机构卖出次数",
            "机构净买额",
            "近1个月涨跌幅",
            "近3个月涨跌幅",
            "近6个月涨跌幅",
            "近1年涨跌幅",
        ]
    ]

    big_df["收盘价"] = pd.to_numeric(big_df["收盘价"], errors="coerce")
    big_df["涨跌幅"] = pd.to_numeric(big_df["涨跌幅"], errors="coerce")
    big_df["龙虎榜成交金额"] = pd.to_numeric(big_df["龙虎榜成交金额"], errors="coerce")
    big_df["上榜次数"] = pd.to_numeric(big_df["上榜次数"], errors="coerce")
    big_df["机构买入额"] = pd.to_numeric(big_df["机构买入额"], errors="coerce")
    big_df["机构买入次数"] = pd.to_numeric(big_df["机构买入次数"], errors="coerce")
    big_df["机构卖出额"] = pd.to_numeric(big_df["机构卖出额"], errors="coerce")
    big_df["机构卖出次数"] = pd.to_numeric(big_df["机构卖出次数"], errors="coerce")
    big_df["机构净买额"] = pd.to_numeric(big_df["机构净买额"], errors="coerce")
    big_df["近1个月涨跌幅"] = pd.to_numeric(big_df["近1个月涨跌幅"], errors="coerce")
    big_df["近3个月涨跌幅"] = pd.to_numeric(big_df["近3个月涨跌幅"], errors="coerce")
    big_df["近6个月涨跌幅"] = pd.to_numeric(big_df["近6个月涨跌幅"], errors="coerce")
    big_df["近1年涨跌幅"] = pd.to_numeric(big_df["近1年涨跌幅"], errors="coerce")
    return big_df

async def stock_lhb_hyyyb_em_today(
) -> pd.DataFrame:
    return await stock_lhb_hyyyb_em(start_date=get_now_year_month_day())


async def stock_lhb_hyyyb_em_lastday(lastday=1
) -> pd.DataFrame:
    
    start_date= get_lastday_year_month_day(lastday)
    return await stock_lhb_hyyyb_em(start_date=start_date)
async def stock_lhb_hyyyb_em_oneday(lastday=1
) -> pd.DataFrame:
    
    start_date= get_lastday_year_month_day(lastday)
    return await stock_lhb_hyyyb_em(start_date=start_date,end_date=start_date)


async def stock_lhb_hyyyb_em(
    start_date: str = "20220324", end_date: str = get_now_year_month_day()
) -> pd.DataFrame:
    """
    东方财富网-数据中心-龙虎榜单-每日活跃营业部
    https://data.eastmoney.com/stock/jgmmtj.html
    :param start_date: 开始日期
    :type start_date: str
    :param end_date: 结束日期
    :type end_date: str
    :return: 每日活跃营业部
    :rtype: pandas.DataFrame
    """
    start_date = "-".join([start_date[:4], start_date[4:6], start_date[6:]])
    end_date = "-".join([end_date[:4], end_date[4:6], end_date[6:]])
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
    params = {
        "sortColumns": "TOTAL_NETAMT,ONLIST_DATE,OPERATEDEPT_CODE",
        "sortTypes": "-1,-1,1",
        "pageSize": "5000",
        "pageNumber": "1",
        "reportName": "RPT_OPERATEDEPT_ACTIVE",
        "columns": "ALL",
        "source": "WEB",
        "client": "WEB",
        "filter": f"(ONLIST_DATE>='{start_date}')(ONLIST_DATE<='{end_date}')",
    }
    data_json = await _get(url, params)

    total_page = data_json["result"]["pages"]
    big_df = pd.DataFrame()

    if False:
        for page in tqdm(range(1, total_page + 1), leave=False):
            params.update({"pageNumber": page})
            data_json = await _get(url, params)
            temp_df = pd.DataFrame(data_json["result"]["data"])
            big_df = pd.concat([big_df, temp_df], ignore_index=True)



    def __update(page):
        params.update({"pageNumber": page})
        return dict(params)

    # 创建任务列表，将每个请求封装成一个任务
    tasks = [_get(url,__update( page)) for page in range(1, total_page + 1)]
    # 等待所有任务完成，获取结果
    responses = await asyncio.gather(*tasks, return_exceptions=True)
    # 解析每个响应，并合并结果
    for data_json in responses:
        temp_df = pd.DataFrame(data_json["result"]["data"])
        big_df = pd.concat([big_df, temp_df], ignore_index=True)



    big_df.reset_index(inplace=True)
    big_df["index"] = big_df.index + 1
    big_df.columns = [
        "序号",
        "营业部名称",
        "上榜日",
        "买入个股数",
        "卖出个股数",
        "买入总金额",
        "卖出总金额",
        "总买卖净额",
        "-",
        "-",
        "买入股票",
        "-",
        "-",
    ]
    big_df = big_df[
        [
            "序号",
            "营业部名称",
            "上榜日",
            "买入个股数",
            "卖出个股数",
            "买入总金额",
            "卖出总金额",
            "总买卖净额",
            "买入股票",
        ]
    ]

    big_df["上榜日"] = pd.to_datetime(big_df["上榜日"]).dt.date
    big_df["买入个股数"] = pd.to_numeric(big_df["买入个股数"])
    big_df["卖出个股数"] = pd.to_numeric(big_df["卖出个股数"])
    big_df["买入总金额"] = pd.to_numeric(big_df["买入总金额"])
    big_df["卖出总金额"] = pd.to_numeric(big_df["卖出总金额"])
    big_df["总买卖净额"] = pd.to_numeric(big_df["总买卖净额"])
    return big_df


async def aio_stock_lhb_yybph_em(symbol: str = "近一月") -> pd.DataFrame:
    """
    东方财富网-数据中心-龙虎榜单-营业部排行
    https://data.eastmoney.com/stock/yybph.html
    :param symbol: choice of {"近一月", "近三月", "近六月", "近一年"}
    :type symbol: str
    :return: 营业部排行
    :rtype: pandas.DataFrame
    """
    symbol_map = {
        "近一月": "01",
        "近三月": "02",
        "近六月": "03",
        "近一年": "04",
    }
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
    params = {
        "sortColumns": "TOTAL_BUYER_SALESTIMES_1DAY,OPERATEDEPT_CODE",
        "sortTypes": "-1,1",
        "pageSize": "5000",
        "pageNumber": "1",
        "reportName": "RPT_RATEDEPT_RETURNT_RANKING",
        "columns": "ALL",
        "source": "WEB",
        "client": "WEB",
        "filter": f'(STATISTICSCYCLE="{symbol_map[symbol]}")',
    }
    data_json = await _get(url, params)
    total_page = data_json["result"]["pages"]
    big_df = pd.DataFrame()

    if False:
        for page in tqdm(range(1, total_page + 1), leave=False):
            params.update({"pageNumber": page})
            data_json = await _get(url, params)
            temp_df = pd.DataFrame(data_json["result"]["data"])
            big_df = pd.concat([big_df, temp_df], ignore_index=True)

    def __update(page):
        params.update({"pageNumber": page})
        return dict(params)

    # 创建任务列表，将每个请求封装成一个任务
    tasks = [_get(url, __update(page)) for page in range(1, total_page + 1)]
    # 等待所有任务完成，获取结果
    responses = await asyncio.gather(*tasks, return_exceptions=True)
    # 解析每个响应，并合并结果
    for data_json in responses:
        temp_df = pd.DataFrame(data_json["result"]["data"])
        big_df = pd.concat([big_df, temp_df], ignore_index=True)




    big_df.reset_index(inplace=True)
    big_df["index"] = big_df.index + 1
    big_df.rename(
        columns={
            "index": "序号",
            "OPERATEDEPT_NAME": "营业部名称",
            "TOTAL_BUYER_SALESTIMES_1DAY": "上榜后1天-买入次数",
            "AVERAGE_INCREASE_1DAY": "上榜后1天-平均涨幅",
            "RISE_PROBABILITY_1DAY": "上榜后1天-上涨概率",
            "TOTAL_BUYER_SALESTIMES_2DAY": "上榜后2天-买入次数",
            "AVERAGE_INCREASE_2DAY": "上榜后2天-平均涨幅",
            "RISE_PROBABILITY_2DAY": "上榜后2天-上涨概率",
            "TOTAL_BUYER_SALESTIMES_3DAY": "上榜后3天-买入次数",
            "AVERAGE_INCREASE_3DAY": "上榜后3天-平均涨幅",
            "RISE_PROBABILITY_3DAY": "上榜后3天-上涨概率",
            "TOTAL_BUYER_SALESTIMES_5DAY": "上榜后5天-买入次数",
            "AVERAGE_INCREASE_5DAY": "上榜后5天-平均涨幅",
            "RISE_PROBABILITY_5DAY": "上榜后5天-上涨概率",
            "TOTAL_BUYER_SALESTIMES_10DAY": "上榜后10天-买入次数",
            "AVERAGE_INCREASE_10DAY": "上榜后10天-平均涨幅",
            "RISE_PROBABILITY_10DAY": "上榜后10天-上涨概率",
        },
        inplace=True,
    )
    big_df = big_df[
        [
            "序号",
            "营业部名称",
            "上榜后1天-买入次数",
            "上榜后1天-平均涨幅",
            "上榜后1天-上涨概率",
            "上榜后2天-买入次数",
            "上榜后2天-平均涨幅",
            "上榜后2天-上涨概率",
            "上榜后3天-买入次数",
            "上榜后3天-平均涨幅",
            "上榜后3天-上涨概率",
            "上榜后5天-买入次数",
            "上榜后5天-平均涨幅",
            "上榜后5天-上涨概率",
            "上榜后10天-买入次数",
            "上榜后10天-平均涨幅",
            "上榜后10天-上涨概率",
        ]
    ]

    big_df["上榜后1天-买入次数"] = pd.to_numeric(big_df["上榜后1天-买入次数"], errors="coerce")
    big_df["上榜后1天-平均涨幅"] = pd.to_numeric(big_df["上榜后1天-平均涨幅"], errors="coerce")
    big_df["上榜后1天-上涨概率"] = pd.to_numeric(big_df["上榜后1天-上涨概率"], errors="coerce")

    big_df["上榜后2天-买入次数"] = pd.to_numeric(big_df["上榜后2天-买入次数"], errors="coerce")
    big_df["上榜后2天-平均涨幅"] = pd.to_numeric(big_df["上榜后2天-平均涨幅"], errors="coerce")
    big_df["上榜后2天-上涨概率"] = pd.to_numeric(big_df["上榜后2天-上涨概率"], errors="coerce")

    big_df["上榜后3天-买入次数"] = pd.to_numeric(big_df["上榜后3天-买入次数"], errors="coerce")
    big_df["上榜后3天-平均涨幅"] = pd.to_numeric(big_df["上榜后3天-平均涨幅"], errors="coerce")
    big_df["上榜后3天-上涨概率"] = pd.to_numeric(big_df["上榜后3天-上涨概率"], errors="coerce")

    big_df["上榜后5天-买入次数"] = pd.to_numeric(big_df["上榜后5天-买入次数"], errors="coerce")
    big_df["上榜后5天-平均涨幅"] = pd.to_numeric(big_df["上榜后5天-平均涨幅"], errors="coerce")
    big_df["上榜后5天-上涨概率"] = pd.to_numeric(big_df["上榜后5天-上涨概率"], errors="coerce")

    big_df["上榜后10天-买入次数"] = pd.to_numeric(big_df["上榜后10天-买入次数"], errors="coerce")
    big_df["上榜后10天-平均涨幅"] = pd.to_numeric(big_df["上榜后10天-平均涨幅"], errors="coerce")
    big_df["上榜后10天-上涨概率"] = pd.to_numeric(big_df["上榜后10天-上涨概率"], errors="coerce")
    return big_df


async def aio_stock_lhb_traderstatistic_em(symbol: str = "近一月") -> pd.DataFrame:
    """
    东方财富网-数据中心-龙虎榜单-营业部统计
    https://data.eastmoney.com/stock/traderstatistic.html
    :param symbol: choice of {"近一月", "近三月", "近六月", "近一年"}
    :type symbol: str
    :return: 营业部统计
    :rtype: pandas.DataFrame
    """
    symbol_map = {
        "近一月": "01",
        "近三月": "02",
        "近六月": "03",
        "近一年": "04",
    }
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
    params = {
        "sortColumns": "AMOUNT,OPERATEDEPT_CODE",
        "sortTypes": "-1,1",
        "pageSize": "5000",
        "pageNumber": "1",
        "reportName": "RPT_OPERATEDEPT_LIST_STATISTICS",
        "columns": "ALL",
        "source": "WEB",
        "client": "WEB",
        "filter": f'(STATISTICSCYCLE="{symbol_map[symbol]}")',
    }
    data_json = await _get(url, params)
    total_page = data_json["result"]["pages"]
    big_df = pd.DataFrame()
    if False:
        for page in tqdm(range(1, total_page + 1), leave=False):
            params.update({"pageNumber": page})
            data_json = await _get(url, params)
            temp_df = pd.DataFrame(data_json["result"]["data"])
            big_df = pd.concat([big_df, temp_df], ignore_index=True)

    def __update(page):
        params.update({"pageNumber": page})
        return dict(params)

    # 创建任务列表，将每个请求封装成一个任务
    tasks = [_get(url, __update(page)) for page in range(1, total_page + 1)]
    # 等待所有任务完成，获取结果
    responses = await asyncio.gather(*tasks, return_exceptions=True)
    # 解析每个响应，并合并结果
    for data_json in responses:
        temp_df = pd.DataFrame(data_json["result"]["data"])
        big_df = pd.concat([big_df, temp_df], ignore_index=True)



    big_df.reset_index(inplace=True)
    big_df["index"] = big_df.index + 1
    big_df.rename(
        columns={
            "index": "序号",
            "OPERATEDEPT_NAME": "营业部名称",
            "AMOUNT": "龙虎榜成交金额",
            "SALES_ONLIST_TIMES": "上榜次数",
            "ACT_BUY": "买入额",
            "TOTAL_BUYER_SALESTIMES": "买入次数",
            "ACT_SELL": "卖出额",
            "TOTAL_SELLER_SALESTIMES": "卖出次数",
        },
        inplace=True,
    )
    big_df = big_df[
        [
            "序号",
            "营业部名称",
            "龙虎榜成交金额",
            "上榜次数",
            "买入额",
            "买入次数",
            "卖出额",
            "卖出次数",
        ]
    ]

    big_df["龙虎榜成交金额"] = pd.to_numeric(big_df["龙虎榜成交金额"], errors="coerce")
    big_df["上榜次数"] = pd.to_numeric(big_df["上榜次数"], errors="coerce")
    big_df["买入额"] = pd.to_numeric(big_df["买入额"], errors="coerce")
    big_df["买入次数"] = pd.to_numeric(big_df["买入次数"], errors="coerce")
    big_df["卖出额"] = pd.to_numeric(big_df["卖出额"], errors="coerce")
    big_df["卖出次数"] = pd.to_numeric(big_df["卖出次数"], errors="coerce")
    return big_df


async def aio_stock_lhb_stock_detail_date_em(symbol: str = "600077") -> pd.DataFrame:
    """
    东方财富网-数据中心-龙虎榜单-个股龙虎榜详情-日期
    https://data.eastmoney.com/stock/tradedetail.html
    :param symbol: 股票代码
    :type symbol: str
    :return: 个股龙虎榜详情-日期
    :rtype: pandas.DataFrame
    """
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
    params = {
        "reportName": "RPT_LHB_BOARDDATE",
        "columns": "SECURITY_CODE,TRADE_DATE,TR_DATE",
        "filter": f'(SECURITY_CODE="{symbol}")',
        "pageNumber": "1",
        "pageSize": "1000",
        "sortTypes": "-1",
        "sortColumns": "TRADE_DATE",
        "source": "WEB",
        "client": "WEB",
    }
    data_json = await _get(url, params)
    temp_df = pd.DataFrame(data_json["result"]["data"])
    temp_df.reset_index(inplace=True)
    temp_df["index"] = temp_df.index + 1
    temp_df.columns = [
        "序号",
        "股票代码",
        "交易日",
        "-",
    ]
    temp_df = temp_df[
        [
            "序号",
            "股票代码",
            "交易日",
        ]
    ]
    temp_df["交易日"] = pd.to_datetime(temp_df["交易日"]).dt.date
    return temp_df


async def aio_tock_lhb_stock_detail_em(
    symbol: str = "000788", date: str = "20220315", flag: str = "卖出"
) -> pd.DataFrame:
    """
    东方财富网-数据中心-龙虎榜单-个股龙虎榜详情
    https://data.eastmoney.com/stock/lhb/600077.html
    :param symbol: 股票代码
    :type symbol: str
    :param date: 查询日期; 需要通过 ak.stock_lhb_stock_detail_date_em(symbol="600077") 接口获取相应股票的有龙虎榜详情数据的日期
    :type date: str
    :param flag: choice of {"买入", "卖出"}
    :type flag: str
    :return: 个股龙虎榜详情
    :rtype: pandas.DataFrame
    """
    flag_map = {
        "买入": "BUY",
        "卖出": "SELL",
    }
    report_map = {
        "买入": "RPT_BILLBOARD_DAILYDETAILSBUY",
        "卖出": "RPT_BILLBOARD_DAILYDETAILSSELL",
    }
    url = "https://datacenter-web.eastmoney.com/api/data/v1/get"
    params = {
        "reportName": report_map[flag],
        "columns": "ALL",
        "filter": f"""(TRADE_DATE='{'-'.join([date[:4], date[4:6], date[6:]])}')(SECURITY_CODE="{symbol}")""",
        "pageNumber": "1",
        "pageSize": "500",
        "sortTypes": "-1",
        "sortColumns": flag_map[flag],
        "source": "WEB",
        "client": "WEB",
        "_": "1647338693644",
    }
    data_json = await _get(url, params)


    temp_df = pd.DataFrame(data_json["result"]["data"])
    temp_df.reset_index(inplace=True)
    temp_df["index"] = temp_df.index + 1

    if flag == "买入":
        temp_df.columns = [
            "序号",
            "-",
            "-",
            "-",
            "-",
            "交易营业部名称",
            "类型",
            "-",
            "-",
            "-",
            "-",
            "买入金额",
            "卖出金额",
            "净额",
            "-",
            "-",
            "-",
            "-",
            "买入金额-占总成交比例",
            "卖出金额-占总成交比例",
            "-",
        ]
        temp_df = temp_df[
            [
                "序号",
                "交易营业部名称",
                "买入金额",
                "买入金额-占总成交比例",
                "卖出金额",
                "卖出金额-占总成交比例",
                "净额",
                "类型",
            ]
        ]
        temp_df["买入金额"] = pd.to_numeric(temp_df["买入金额"])
        temp_df["买入金额-占总成交比例"] = pd.to_numeric(temp_df["买入金额-占总成交比例"])
        temp_df["卖出金额"] = pd.to_numeric(temp_df["卖出金额"])
        temp_df["卖出金额-占总成交比例"] = pd.to_numeric(temp_df["卖出金额-占总成交比例"])
        temp_df.sort_values("类型", inplace=True)
        temp_df.reset_index(inplace=True, drop=True)
        temp_df["序号"] = range(1, len(temp_df["序号"]) + 1)
    else:
        temp_df.columns = [
            "序号",
            "-",
            "-",
            "-",
            "-",
            "交易营业部名称",
            "类型",
            "-",
            "-",
            "-",
            "-",
            "买入金额",
            "卖出金额",
            "净额",
            "-",
            "-",
            "-",
            "-",
            "买入金额-占总成交比例",
            "卖出金额-占总成交比例",
            "-",
        ]
        temp_df = temp_df[
            [
                "序号",
                "交易营业部名称",
                "买入金额",
                "买入金额-占总成交比例",
                "卖出金额",
                "卖出金额-占总成交比例",
                "净额",
                "类型",
            ]
        ]
        temp_df["买入金额"] = pd.to_numeric(temp_df["买入金额"])
        temp_df["买入金额-占总成交比例"] = pd.to_numeric(temp_df["买入金额-占总成交比例"])
        temp_df["卖出金额"] = pd.to_numeric(temp_df["卖出金额"])
        temp_df["卖出金额-占总成交比例"] = pd.to_numeric(temp_df["卖出金额-占总成交比例"])
        temp_df.sort_values("类型", inplace=True)
        temp_df.reset_index(inplace=True, drop=True)
        temp_df["序号"] = range(1, len(temp_df["序号"]) + 1)
    return temp_df




async  def test_all_func():

    stock_lhb_detail_em_df =await aio_stock_lhb_detail_em(
        start_date="20240403"
    )
    print(stock_lhb_detail_em_df)
    stock_lhb_detail_em_df =await aio_stock_lhb_detail_em(
        start_date="20230403", end_date="20230417"
    )
    print(stock_lhb_detail_em_df)


    stock_lhb_stock_statistic_em_df =await aio_stock_lhb_stock_statistic_em(symbol="近一月")
    print(stock_lhb_stock_statistic_em_df)

    stock_lhb_stock_statistic_em_df =await aio_stock_lhb_stock_statistic_em(symbol="近三月")
    print(stock_lhb_stock_statistic_em_df)

    stock_lhb_stock_statistic_em_df =await aio_stock_lhb_stock_statistic_em(symbol="近六月")
    print(stock_lhb_stock_statistic_em_df)

    stock_lhb_stock_statistic_em_df =await aio_stock_lhb_stock_statistic_em(symbol="近一年")
    print(stock_lhb_stock_statistic_em_df)

    stock_lhb_jgmmtj_em_df =await aio_stock_lhb_jgmmtj_em(
        start_date="20240101", end_date="20240906"
    )
    print(stock_lhb_jgmmtj_em_df)

    stock_lhb_jgstatistic_em_df =await aio_stock_lhb_jgstatistic_em(symbol="近一月")
    print(stock_lhb_jgstatistic_em_df)

    stock_lhb_hyyyb_em_df =await stock_lhb_hyyyb_em(
        start_date="20220324", end_date="20220324"
    )
    print(stock_lhb_hyyyb_em_df)

    stock_lhb_yybph_em_df =await aio_stock_lhb_yybph_em(symbol="近一月")
    print(stock_lhb_yybph_em_df)

    stock_lhb_traderstatistic_em_df =await aio_stock_lhb_traderstatistic_em(symbol="近一月")
    print(stock_lhb_traderstatistic_em_df)

    stock_lhb_stock_detail_date_em_df =await aio_stock_lhb_stock_detail_date_em(symbol="002901")
    print(stock_lhb_stock_detail_date_em_df)

    stock_lhb_stock_detail_em_df =await aio_tock_lhb_stock_detail_em(
        symbol="002901", date="20221012", flag="买入"
    )
    print(stock_lhb_stock_detail_em_df)

    stock_lhb_stock_detail_em_df = await aio_tock_lhb_stock_detail_em(
        symbol="600016", date="20220324", flag="买入"
    )
    print(stock_lhb_stock_detail_em_df)


async  def test_day():
    t1= await aio_stock_lhb_detail_em_today()
    print(t1)
    t2= await aio_stock_lhb_detail_em_lastday(lastday=1)
    print(t2)

def get_radios2(df)->pd.Series:
    """
    return Series
    (-30.101000000000003, -29.95]    0.000000
    (-29.95, -20.05]                 0.000000
    (-20.05, -19.95]                 0.000000
    (-19.95, -10.05]                 0.000000
    (-10.05, -9.95]                  0.000000
    (-9.95, -7.0]                    0.000000
    (-7.0, -4.0]                     0.000000
    (-4.0, -2.0]                     0.000000
    (-2.0, -0.01]                    0.352941
    (-0.01, 0.01]                    0.176471
    (0.01, 2.0]                      0.470588
    (2.0, 4.0]                       0.000000
    (4.0, 7.0]                       0.000000
    (7.0, 9.95]                      0.000000
    (9.95, 10.05]                    0.000000
    (10.05, 19.95]                   0.000000
    (19.95, 20.05]                   0.000000
    (20.05, 29.95]                   0.000000
    (29.95, 30.05]                   0.000000
    """
    if isinstance(df,list):
        data = {"increase": df}

        # 将字典转换为DataFrame
        df = pd.DataFrame.from_dict(data)

    r = df['increase'].value_counts(normalize=True, sort=False,
                                    bins=[-30.05, -29.95, -20.05, -19.95, -10.05, -9.95, -7, -4, -2, -0.01, 0.01, 2, 4,
                                          7, 9.95, 10.05, 19.95, 20.05, 29.95, 30.05])
    # print(r)
    return r

def get_radios3(df)->pd.Series:
    """

    """
    if isinstance(df,list):
        data = {"increase": df}

        # 将字典转换为DataFrame
        df = pd.DataFrame.from_dict(data)

    r = df['increase'].value_counts(normalize=True, sort=False,
                                    bins=[-30.05,  -20.05,-10.05,  -0.01, 0.01,  10.05, 20.05,  30.05])
    # print(r)
    return r




async  def test_analysis():
    df= await aio_stock_lhb_detail_em_oneday(lastday=1)
    print(df)
    df["increase"]=df["上榜后1日"]
    r=get_radios3(df)
    print(r)
    df1 = df.query('涨跌幅 > 0') #todo 过滤因 涨跌幅 >0 入榜
    r=get_radios3(df1)
    print(r)

    df2 = df[df['名称'].str.contains('ST', case=False, na=False)] # todo st
    r=get_radios3(df2)
    print(r)

    df22 = df2.query('涨跌幅 > 0') #todo 过滤因 涨跌幅 >0 入榜 ,st
    r=get_radios3(df22)
    print(r)

    df3 = df[~df['名称'].str.contains('ST', case=False, na=False)] # todo 去除st
    r=get_radios3(df3)
    print(r)

    df32 = df3.query('涨跌幅 > 0') #todo 过滤因 涨跌幅 >0 入榜 ,去除st
    r=get_radios3(df32)
    print(r)

    #todo 过滤因 涨跌幅 >0 入榜 ,去除st
    for n,v in enumerate(["实力游资买入","主力做T","卖一主卖","买一主买","机构买入","机构卖出"]):
        df32 = df[df['解读'].str.contains(v, case=False, na=False)] #todo 包含上述词条
        r = get_radios3(df32)
        print(v,"\n",r)


async def get_analysis_word():
    df= await aio_stock_lhb_detail_em_lastday(lastday=256)
    print(df["解读"])


if __name__ == "__main__":
    print(__name__)

    import asyncio
    import time
    t=time.time()

    # asyncio.run(test_all_func())
    # asyncio.run(test_day())
    # asyncio.run(test_analysis())
    asyncio.run(get_analysis_word())

    print("花费时间:",time.time()-t)
