
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20240607"


def part(ind: Indicator, topn: int = 2):
    """
    选取因子值最高的 topn 支证券

    :param Indicator ind: 单因子
    :param int topn: 选取因子值最高的 topn 支证券
    """
    my_se = SE_MultiFactor([ind], topn=topn, mode="MF_EqualWeight")
    return my_se


if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        print("ignore test")
        exit(0)

    # 请在下方编写测试代码
    ind = part(MA())
    print(ind)
