
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20240912"


def _calculate(self):
    self.stk_sys_dict = dict([(s.get_stock(), s)
                             for s in self.real_sys_list])
    separate_hold = self.get_param("separate_hold")
    ind = get_part('default.ind.通达信百变一阳指') if self.filter_ind is None else get_part(
        'default.ind.通达信百变一阳指') & (self.filter_ind)
    for sys in self.real_sys_list:
        k = sys.to
        x = ind(k)
        for i in range(len(k)):
            val = x[i]
            if val != constant.null_price and val >= 1.0:
                date = k[i].datetime
                if date in self.selected:
                    if separate_hold == 0:
                        self.selected[date].append(SystemWeight(sys, 1.0))
                    else:
                        if len(self.selected[date]) < separate_hold:
                            self.selected[date].append(SystemWeight(sys, 1.0))
                else:
                    self.selected[date] = [SystemWeight(sys, 1.0)]

    # 插入权重为0的空系统进行占位
    if separate_hold > 0:
        for k in self.selected:
            if 0 < len(self.selected[k]) < separate_hold:
                for i in range(len(self.selected[k]), separate_hold):
                    self.selected[k].append(SystemWeight(None, 0.0))


@hku_catch(ret=[], trace=True)
def _get_selected(self, date):
    return self.selected[date] if date in self.selected else []


def part(stks, separate_hold=0, filter_ind=None):
    """
    通达信一阳指指标选择器, 如果指标值大于等于1则选中

    :param list stks: 证券列表
    :param int separate_hold: 分仓数量，允许持有的不同证券的数量。如为5时，最多允许同时持有5支证券，
            如果当天仅有1支满足条件被选出，那么该证券仅能占用5分之一的资产。如果为0时，不受限制，如果当天
            仅有1支满足条件，则该支将使用全部可用资金。该参数需要配合 AF 中的 ignore_zero_weight 才会生效。
    :param Indicator fiter_ind: 和一阳指指标进行与操作，以便进行过滤，比如过滤当前收盘价大于10元, CLOSE() > 10
    """
    hku_check(separate_hold >= 0,
              f"Invalid input separate_hold: {separate_hold}")

    local_hub = get_current_hub(__file__)
    my_sys = get_part(f'{local_hub}.sys.调仓日买入')
    my_sys.set_param("buy_delay", False)
    my_sys.set_param("sell_delay", False)

    my_se = crtSE(_calculate, get_selected=_get_selected,
                  params=dict(separate_hold=separate_hold))
    my_se.selected = {}
    my_se.filter_ind = filter_ind
    my_se.name = "SE_指标_通达信一阳指"
    my_se.add_stock_list(stks, my_sys)
    return my_se


if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        print("ignore test")
        exit(0)

    import os
    import sys
    if sys.platform == 'win32':
        os.system('chcp 65001')

    # 仅加载测试需要的数据，请根据需要修改
    options = {
        "stock_list": ["sz000001"],
        "ktype_list": ["day"],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False,
        "spot_worker_num": 1,
    }
    load_hikyuu(**options)

    # 请在下方编写测试代码
    se = part(stks=[sm['sz000001']])
    print(se)
