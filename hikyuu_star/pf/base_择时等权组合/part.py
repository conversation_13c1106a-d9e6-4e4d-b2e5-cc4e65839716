
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from typing import Sequence
from hikyuu import *

author = "admin"
version = "20240528"


def part(tm: TradeManager, sys: System, stks: Sequence, hold_n: int = 10, buy_delay: bool = False,
         adjust_cycle: int = 10, adjust_mode: str = "query",
         delay_to_trading_day: bool = True) -> Portfolio:
    """
    该投资组合为基础的择时等权组合，通过指定 sys 编写实际的择时等权组合
    调仓日以 SYS 系统策略实例的买入/卖出信号为准执行实际买入/卖出，在下一个调仓日之前依据 SYS 的卖出操作执行实际卖出操作。

    :param TradeManager tm: 账户管理实例
    :param System sys: 单标的系统交易策略原型
    :param Sequence stks: 投资组合目标证券集合
    :param hold_n n: 资金按 1 / holo_n 比例等权分配，也同时代表最多可以同时持有的证券种类数量，
                     如果 hold_n 小于等于0，则按调仓日实际选中的交易策略数量等比分配资金（即不限数量）
    :param bool buy_delay: 买入在第二日开盘时执行，默认为 False (即收盘时买入)
    :param int adjust_cycle: 调仓周期，默认10个交易日
    :param str adjust_mode: 调仓方式
    :param bool delay_to_trading_day: 非交易日调仓时是否延迟到交易日        
    """
    hku_check(len(stks) > 0, "未指定证券集合")
    my_af = AF_FixedWeight(1.0/hold_n) if hold_n > 0 else AF_EqualWeight()
    sys.set_param("buy_delay", buy_delay)
    # sys.set_param("sell_delay", buy_delay)
    my_se = SE_Signal(stks, sys)
    my_pf = PF_Simple(tm=tm, af=my_af, se=my_se, adjust_cycle=adjust_cycle,
                      adjust_mode=adjust_mode, delay_to_trading_day=delay_to_trading_day)
    return my_pf
