
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20240518"


def part(tm: TradeManager, stks, n=10, band=0.5, adjust_cycle: int = 10, adjust_mode: str = "query",
         delay_to_trading_day: bool = True, trade_on_close: bool = True,
         sys_use_self_tm: bool = False, sell_at_not_selected: bool = False):
    """doc"""
    local_hub = get_current_hub(__file__)
    my_sys = get_part(f"{local_hub}.sys.趋势布林带", n=n, band=band)
    my_sys.mm = MM_FixedCapitalFunds(20)
    my_pf = get_part(f"{local_hub}.pf.base_择时选股组合", tm=tm, sys=my_sys,
                     stks=stks,
                     adjust_cycle=adjust_cycle, adjust_mode=adjust_mode,
                     delay_to_trading_day=delay_to_trading_day, trade_on_close=trade_on_close,
                     sys_use_self_tm=sys_use_self_tm, sell_at_not_selected=sell_at_not_selected)
    return my_pf


if __name__ == "__main__":
    if len(sys.argv) > 1:
        exit(0)

    import os
    import sys
    if sys.platform == 'win32':
        os.system('chcp 65001')

    options = {
        "stock_list": ['SH510300', 'SH510500', 'SZ159915', 'SH512690',
                       'SH515050', 'SH512010', 'SH518880', 'SH511260'],
        "ktype_list": ['day'],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False
    }
    load_hikyuu(**options)

    local_hub = get_current_hub(__file__)
    # update_hub(local_hub)

    stks = [sm[code] for code in options["stock_list"]]

    my_tm = crtTM()
    my_pf = get_part(f"{local_hub}.pf.择时选股组合_趋势布林带",
                     tm=my_tm, stks=stks, adjust_cycle=1)

    print(my_pf)

    if len(sys.argv) <= 1:
        start_date = Datetime(20150101)
        end_date = None
        query = Query(start_date, end_date)
        cost_func = TC_FixedA2017()

        my_pf.tm = crtTM(start_date, init_cash=1000000, cost_func=cost_func)
        my_pf.set_param("trace", True)

        my_pf.run(query)
        my_pf.performance()
        my_pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))

        import matplotlib.pylab as plt
        plt.show()
