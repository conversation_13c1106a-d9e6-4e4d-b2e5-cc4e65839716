
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20240518"


def part(tm: TradeManager, stks, ref_stk=None, hold_n: int = 10, fast_n: int = 10,
         slow_n: int = 120, buy_delay: bool = False,
         adjust_cycle: int = 10, adjust_mode: str = "query",
         delay_to_trading_day: bool = True):
    """doc"""
    local_hub = get_current_hub(__file__)
    my_sys = get_part(f"{local_hub}.sys.趋势双均线", fast_n=fast_n, slow_n=slow_n)
    my_pf = get_part(f"{local_hub}.pf.base_择时等权组合", tm=tm, sys=my_sys,
                     stks=stks, hold_n=hold_n, buy_delay=buy_delay, adjust_cycle=adjust_cycle,
                     adjust_mode=adjust_mode, delay_to_trading_day=delay_to_trading_day)
    return my_pf


if __name__ == "__main__":
    if len(sys.argv) > 1:
        exit(0)

    import os
    import sys
    if sys.platform == 'win32':
        os.system('chcp 65001')

    load_hikyuu()

    local_hub = get_current_hub(__file__)
    update_hub(local_hub)

    stks = get_part(f"{local_hub}.other.stks-沪深300")

    my_tm = crtTM(Datetime(20200101), init_cash=1000000)
    my_pf = get_part(f"{local_hub}.pf.择时等权组合_趋势双均线",
                     tm=my_tm, stks=stks, adjust_cycle=30)

    print(my_pf)

    if len(sys.argv) <= 1:
        my_pf.set_param("trace", True)
        start_date = Datetime(20150101)
        end_date = None
        query = Query(start_date, end_date)
        cost_func = TC_FixedA2017()
        my_pf.tm = crtTM(start_date, init_cash=100000, cost_func=cost_func)
        my_pf.run(query)
        my_pf.performance()
        my_pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))

        import matplotlib.pylab as plt
        plt.show()
