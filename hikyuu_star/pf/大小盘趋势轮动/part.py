
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from typing import Sequence
from hikyuu import *

author = "admin"
version = "20240926"


def part(tm: TradeManager, stks: Sequence, mm=None, adjust_cycle: int = 10, adjust_mode: str = "query",
         delay_to_trading_day: bool = True):
    """
    大小盘趋势轮动

    :param TradeManager tm: 交易账户
    :param Sequence stks: 证券列表，长度为2
    :param MM mm: 资金管理策略，默认为 MM_Nothing()
    :param int adjust_cycle: 调仓周期，默认10个交易日
    :param str adjust_mode: 调仓方式
    :param bool delay_to_trading_day: 非交易日调仓时是否延迟到交易日       
    """
    hku_check(len(stks) == 2,
              f"The lenght of stks must be 2! But current len: {len(stks)}")
    roc_20 = ROCP(CLOSE, 20)

    my_sg = SG_Bool(roc_20 > 0.02, roc_20 < -0.02)
    my_mm = MM_Nothing() if mm is None else mm
    my_sys = SYS_Simple(mm=my_mm, sg=my_sg)
    my_sys.set_param("buy_delay", False)
    my_sys.set_param("sell_delay", False)

    local_hub = get_current_hub(__file__)
    my_pf = get_part(f'{local_hub}.pf.base_单因子轮动', tm=tm,
                     ind=roc_20, topn=2, stks=stks, sys=my_sys, adjust_cycle=adjust_cycle,
                     adjust_mode=adjust_mode, delay_to_trading_day=delay_to_trading_day)
    my_pf.name = "大小盘趋势轮动"
    return my_pf


if __name__ == "__main__":
    options = {
        "stock_list": ['sh510300', 'sz159915'],
        "ktype_list": ['day'],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False
    }
    load_hikyuu(**options)

    local_hub = get_current_hub(__file__)
    update_hub(local_hub)

    start_date = Datetime(20240101)
    end_date = None
    query = Query(start_date, end_date)

    # 请在下方编写测试代码
    stks = tuple([sm[code] for code in options["stock_list"]])

    # 不带交易成本
    # my_tm = crtTM(start_date, init_cash=100000, cost_func=TC_FixedA2017())

    # 带交易成本
    my_tm = crtTM(start_date, init_cash=100000, cost_func=TC_FixedA2017())

    pf = part(tm=my_tm, stks=stks, adjust_cycle=10)
    print(pf)

    if len(sys.argv) <= 1:
        pf.run(query)
        pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))

        from matplotlib import pylab as plt
        pf.performance()
        plt.show()
