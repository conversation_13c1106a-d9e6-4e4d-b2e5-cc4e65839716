
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from typing import Sequence
from hikyuu import *

author = "admin"
version = "20240526"


def part(tm: TradeManager, stks: Sequence, hold_n: int = 10, buy_delay: bool = False,
         adjust_cycle: int = 10, adjust_mode: str = "query",
         delay_to_trading_day: bool = True) -> Portfolio:
    """
    简易波动指标（EMV）信号系统等权组合

    :param TradeManager tm: 账户管理实例
    :param Sequence stks: 投资组合目标证券集合
    :param hold_n n: 资金按 1 / holo_n 比例等权分配，也同时代表最多可以同时持有的证券种类数量，
                     如果 hold_n 小于等于0，则按调仓日实际选中的交易策略数量等比分配资金（即不限数量）
    :param bool buy_delay: 买入在第二日开盘时执行，默认为 False (即收盘时买入)
    :param int adjust_cycle: 调仓周期，默认10个交易日
    :param str adjust_mode: 调仓方式
    :param bool delay_to_trading_day: 非交易日调仓时是否延迟到交易日       
    """
    local_hub = get_current_hub(__file__)
    my_sys = get_part(f"{local_hub}.sys.emv择时")
    my_pf = get_part(f"{local_hub}.pf.base_择时等权组合", tm=tm, sys=my_sys.clone(),
                     stks=stks, hold_n=hold_n, buy_delay=buy_delay, adjust_cycle=adjust_cycle,
                     adjust_mode=adjust_mode, delay_to_trading_day=delay_to_trading_day)
    return my_pf


if __name__ == "__main__":
    if len(sys.argv) > 1:
        exit(0)

    import os
    import sys
    if sys.platform == 'win32':
        os.system('chcp 65001')

    load_hikyuu()

    local_hub = get_current_hub(__file__)
    update_hub(local_hub)

    stks = get_part(f"{local_hub}.other.stks-沪深300")

    my_tm = crtTM(Datetime(20200101), init_cash=1000000)
    my_pf = get_part(f"{local_hub}.pf.择时等权组合_emv",
                     tm=my_tm, stks=stks, hold_n=10, adjust_cycle=5)
    print(my_pf)

    if len(sys.argv) <= 1:
        my_pf.set_param("trace", True)
        start_date = Datetime(20140601)
        # start_date = Datetime(20200101)
        end_date = None  # Datetime(20241201)
        query = Query(start_date, end_date)
        # my_pf.tm = crtTM(start_date, init_cash=100000)
        cost_func = TC_FixedA2017()
        # cost_func = TC_Zero()
        my_pf.tm = crtTM(start_date, init_cash=100000,
                         cost_func=cost_func)
        my_pf.run(query)
        my_pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
        my_pf.performance()

        import matplotlib.pylab as plt
        plt.show()
