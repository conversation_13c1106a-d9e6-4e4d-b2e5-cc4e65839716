
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu.interactive import *
try:
    from .part import *
except:
    from part import *


if __name__ == "__main__":
    from hikyuu.interactive import *
    import matplotlib.pylab as plt

    import sys
    if sys.platform == 'win32':
        import os
        os.system('chcp 65001')

    # 定义回测日期
    start_date = Datetime(20200101)
    end_date = None
    query = Query(start_date, end_date)

    # 获取指数板块沪深300银行成分股
    stks = [s for s in sm.get_block("指数板块", "300银行")]

    # 选择用于判断的指标，我们直接从 default hub 中获取，如果没更新过，记得 update_hub("default")
    市净率 = get_part("default.ind.市净率")

    # 定义一个多因子评分板
    # 这里 MF 在只有一个因子的情况下，将直接使用因子值本身进行排序
    my_mf = MF_EqualWeight([REF(市净率, 1)], stks, query)

    # 先建立一个股票到实际运行的系统策略的映射
    def calculate(self):
        self.stk_sys_dict = dict([(s.get_stock(), s)
                                 for s in self.real_sys_list])

    # 实现选股策略中每日选取的系统
    @hku_catch(ret=ScoreRecordList(), trace=True)
    def get_selected(self, date):
        scores = self.mf.get_scores(date)
        if len(scores) == 0:
            return []
        elif len(scores) == 1 and not isnan(scores[-1].value):
            # print(scores[-1].value)
            return [SystemWeight(self.stk_sys_dict[scores[-1].stock], scores[-1].value),]
        else:
            ret = []
            for i in range(len(scores)-1, -1, -1):
                if not isnan(scores[i].value):
                    ret.append(SystemWeight(
                        self.stk_sys_dict[scores[i].stock], scores[i].value))
                    if len(ret) == 2:
                        break
            return ret

    my_se = crtSE(calculate, get_selected=get_selected)
    my_se.mf = my_mf

    my_sg = SG_Cycle()
    # my_sg = SG_AllwaysBuy()
    my_mm = MM_Nothing()
    # my_sys = SYS_Simple(tm=crtTM(start_date), sg=my_sg, mm=my_mm)
    my_sys = SYS_Simple(sg=my_sg, mm=my_mm)
    my_sys.set_param("buy_delay", False)
    # my_sys.set_param("trace", True)

    my_se = my_se.clone()
    my_se.add_stock_list(stks, my_sys)

    my_af = AF_EqualWeight()
    # , cost_func=TC_FixedA2017())
    my_tm = crtTM(start_date, init_cash=100000, name="PY_SYS")
    pf = PF_Simple(tm=my_tm, af=my_af, se=my_se, adjust_cycle=20)
    pf.set_param("trace", True)
    pf.run(query)
    pf.performance()  # ref_stk=sm['sh880471'])

    my_tm.tocsv(".")
    print(my_tm)
    print(pf)
    plt.show()
