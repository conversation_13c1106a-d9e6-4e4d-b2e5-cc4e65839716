
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from typing import Sequence
from hikyuu import *

author = "admin"
version = "20240528"


def part(tm: TradeManager, sys: System, stks: Sequence,
         adjust_cycle: int = 10, adjust_mode: str = "query",
         delay_to_trading_day: bool = True, trade_on_close: bool = True,
         sys_use_self_tm: bool = False, sell_at_not_selected=False) -> Portfolio:
    """
    基础模式，仅依赖单系统策略信号进行买入/卖出，不通过 AF 进行整体资金分配，资金分配由各原型系统策略中的 MM 自行控制。

    :param TradeManager tm: 账户管理实例
    :param System sys: 单标的系统交易策略原型
    :param Sequence stks: 投资组合目标证券集合
    :param int adjust_cycle: 调仓周期，默认10个交易日
    :param str adjust_mode: 调仓方式
    :param bool delay_to_trading_day: 非交易日调仓时是否延迟到交易日
    :param bool trade_on_close: 在收盘时进行交易
    :param bool sys_use_self_tm: 原型系统交易账户使用自身 tm 账户计算
    :param bool sell_at_not_selected: 调仓日未选中的股票是否强制卖出
    :param 
    """
    hku_check(len(stks) > 0, "未指定证券集合")
    my_se = SE_Signal(stks, sys)
    my_pf = PF_WithoutAF(tm=tm, se=my_se, adjust_cycle=adjust_cycle,
                         adjust_mode=adjust_mode, delay_to_trading_day=delay_to_trading_day,
                         trade_on_close=trade_on_close, sys_use_self_tm=sys_use_self_tm,
                         sell_at_not_selected=sell_at_not_selected)
    my_pf.name = "base_择时选股组合"
    return my_pf


if __name__ == "__main__":
    if len(sys.argv) > 1:
        exit(0)

    import os
    import sys
    if sys.platform == 'win32':
        os.system('chcp 65001')

    options = {
        "stock_list": ['SZ000001', 'SZ000002'],
        "ktype_list": ['day'],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False
    }
    load_hikyuu(**options)

    stks = [sm[code] for code in options["stock_list"]]
    my_tm = crtTM()
    my_sys = get_part("default.sys.趋势双均线")
    pf = part(tm=my_tm, sys=my_sys, stks=stks)
    print(pf)
