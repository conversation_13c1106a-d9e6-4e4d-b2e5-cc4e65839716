
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "root"
version = "20250518"


def part(tm: TradeManager, stk: Stock, pf_param: dict, fast_day_n: int = 5, slow_day_n: int = 20,
         fasst_week_n: int = 5, slow_week_n: int = 20, fast_month_n: int = 5,
         slow_month_n: int = 20, fill_null: bool = True):
    """doc"""
    mm = MM_Nothing()
    local_hub = get_current_hub(__file__)
    sys_day = get_part(f"{local_hub}.sys.扩展周期双均线金叉", fastn=fast_day_n,
                       slown=slow_day_n, ktype=Query.DAY)
    sys_day.mm = mm
    sys_day.name = "日线金叉"
    sys_week = get_part(f"{local_hub}.sys.扩展周期双均线金叉", fastn=fasst_week_n,
                        slown=slow_week_n, ktype=Query.WEEK)
    sys_week.mm = mm
    sys_week.name = "周线金叉"
    sys_month = get_part(f"{local_hub}.sys.扩展周期双均线金叉", fastn=fast_month_n,
                         slown=slow_month_n, ktype=Query.MONTH)
    sys_month.mm = mm
    sys_month.name = "月线金叉"
    my_se = SE_Fixed()
    my_se.add_stock_list([stk], sys_day)
    my_se.add_stock_list([stk], sys_week)
    my_se.add_stock_list([stk], sys_month)

    af = AF_EqualWeight()
    ret = PF_Simple(tm, my_se, af, **pf_param)
    ret.name = "单系统多周期金叉等权组合"
    return ret


if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        ind = part()
        print(ind)
        exit(0)

    import sys
    if sys.platform == 'win32':
        import os
        os.system('chcp 65001')

    # 仅加载测试需要的数据，请根据需要修改
    options = {
        'stock_list': ['sz000001'],
        'ktype_list': ['day'],
        'preload_num': {'day_max': 100000},
        'load_history_finance': False,
        'load_weight': False,
        'start_spot': False,
        'spot_worker_num': 1,
    }
    load_hikyuu(**options)

    start_date = Datetime(20160101)
    end_date = Datetime(20260501)
    query = Query(start_date, end_date)

    stk = sm[options['stock_list'][0]]
    k = stk.get_kdata(Query(-1000))

    # 请在下方编写测试代码
    my_tm = crtTM(start_date, init_cash=100000,
                  cost_func=TC_FixedA2017())

    params = {"adjust_cycle": 1, "adjust_mode": "query",
              "delay_to_trading_day": True}
    pf = part(my_tm, stk, params)

    pf.set_param("trace", True)
    pf.run(query)
    print(pf)

    my_tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
    my_tm.performance(query)

    # 显示图形
    import matplotlib.pylab as plt
    plt.show()
