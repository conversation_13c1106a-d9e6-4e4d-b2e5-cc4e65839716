
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20240912"


def part(tm, stks, separate_hold=0, filter_ind=None, adjust_cycle: int = 10, adjust_mode: str = "query",
         delay_to_trading_day: bool = True):
    local_hub = get_current_hub(__file__)
    my_se = get_part(f'{local_hub}.se.指标_通达信一阳指',
                     stks=stks, separate_hold=separate_hold, filter_ind=filter_ind)
    my_af = AF_EqualWeight()
    if separate_hold > 0:
        my_af.set_param("ignore_zero_weight", False)

    my_pf = PF_Simple(tm=tm, af=my_af, se=my_se, adjust_cycle=adjust_cycle,
                      adjust_mode=adjust_mode, delay_to_trading_day=delay_to_trading_day)
    my_pf.se = my_se
    return my_pf


if __name__ == "__main__":
    if len(sys.argv) > 1:
        exit(0)

    import os
    import sys
    if sys.platform == 'win32':
        os.system('chcp 65001')

    load_hikyuu()

    local_hub = get_current_hub(__file__)
    update_hub(local_hub)

    start_date = Datetime(20200101)
    stks = tuple([s for s in sm if s.valid and s.type in (
        constant.STOCKTYPE_A,) and 'ST' not in s.name])
    # 不带交易成本
    # my_tm = crtTM(start_date, init_cash=100000)
    # 带交易成本
    my_tm = crtTM(start_date, init_cash=100000, cost_func=TC_FixedA2017())
    my_pf = get_part(f"{local_hub}.pf.选股持仓组合_通达信一阳指",
                     tm=my_tm, stks=stks, separate_hold=10, adjust_cycle=1)
    my_pf.run(Query(start_date))
    my_pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
    from matplotlib import pylab as plt
    my_pf.performance()
    plt.show()
