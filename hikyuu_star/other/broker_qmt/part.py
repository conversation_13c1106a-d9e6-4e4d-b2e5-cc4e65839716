
#!/usr/bin/env python
# -*- coding:utf-8 -*-

try:
    from xtquant import xtconstant
    from xtquant.xttype import StockAccount
    from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
    from xtquant import xtdata
    from hikyuu import *

    author = "admin"
    version = "********"

    class MyXtQuantTraderCallback(XtQuantTraderCallback):
        def on_disconnected(self):
            """
            连接断开
            :return:
            """
            print(datetime.datetime.now(), '连接断开回调')

        def on_stock_order(self, order):
            """
            委托回报推送
            :param order: XtOrder对象
            :return:
            """
            print(datetime.datetime.now(), '委托回调 投资备注', order.order_remark)

        def on_stock_trade(self, trade):
            """
            成交变动推送
            :param trade: XtTrade对象
            :return:
            """
            print(datetime.datetime.now(), '成交回调', trade.order_remark,
                  f"委托方向(48买 49卖) {trade.offset_flag} 成交价格 {trade.traded_price} 成交数量 {trade.traded_volume}")

        def on_order_error(self, order_error):
            """
            委托失败推送
            :param order_error:XtOrderError 对象
            :return:
            """
            # print("on order_error callback")
            # print(order_error.order_id, order_error.error_id, order_error.error_msg)
            print(f"委托报错回调 {order_error.order_remark} {order_error.error_msg}")

        def on_cancel_error(self, cancel_error):
            """
            撤单失败推送
            :param cancel_error: XtCancelError 对象
            :return:
            """
            print(datetime.datetime.now(), sys._getframe().f_code.co_name)

        def on_order_stock_async_response(self, response):
            """
            异步下单回报推送
            :param response: XtOrderResponse 对象
            :return:
            """
            print(f"异步委托回调 投资备注: {response.order_remark}")

        def on_cancel_order_stock_async_response(self, response):
            """
            :param response: XtCancelOrderResponse 对象
            :return:
            """
            print(datetime.datetime.now(), sys._getframe().f_code.co_name)

        def on_account_status(self, status):
            """
            :param response: XtAccountStatus 对象
            :return:
            """
            print(datetime.datetime.now(), sys._getframe().f_code.co_name)

    def create_trader(xt_acc, path, session_id):
        trader = XtQuantTrader(
            path, session_id, callback=MyXtQuantTraderCallback())
        trader.start()
        connect_result = trader.connect()
        print("connect_result: ", connect_result)
        trader.subscribe(xt_acc)
        return trader if connect_result == 0 else None

    def try_connect(xt_acc, path):
        session_id_range = [i for i in range(100, 120)]

        import random
        random.shuffle(session_id_range)

        # 遍历尝试session_id列表尝试连接
        for session_id in session_id_range:
            trader = create_trader(xt_acc, path, session_id)
            if trader:
                print(f'连接成功，session_id:{session_id}')
                return trader
            else:
                print('连接失败，session_id:{session_id}，继续尝试下一个id')
                continue

        print('所有id都尝试后仍失败，放弃连接')
        return None

    xt_trader = None

    def get_xttrader(xt_acc, path):
        global xt_trader
        if xt_trader is None:
            xt_trader = try_connect(xt_acc, path)
        return xt_trader

    class QmtTradeOrderBroker:
        def __init__(self, account, path, name, max_asset_percent=0.0):
            self._account = StockAccount(account)
            self._path = path
            self.name = name
            self.max_asset_percent = max_asset_percent  # 允许使用的最大当前总资产比例
            self.buffer = {}

        def buy(self, market, code, price, num, stoploss, goal_price, part_from):
            trader = get_xttrader(self._account, self._path)
            market_code = f"{code}.{market}"
            trader.order_stock_async(self._account, market_code, xtconstant.STOCK_BUY,
                                     num, xtconstant.LATEST_PRICE, price, self.name, f'{stoploss}|{goal_price}|{get_system_part_name(part_from)}')
            print(f"计划买入：{market_code}  {price}  {num}")
            self.buffer[market_code] = (num, stoploss, goal_price)

        def sell(self, market, code, price, num, stoploss, goal_price, part_from):
            trader = get_xttrader(self._account, self._path)
            market_code = f"{code}.{market}"
            trader.order_stock_async(self._account, market_code, xtconstant.STOCK_SELL,
                                     num, xtconstant.LATEST_PRICE, price, self.name, f'{stoploss}|{goal_price}|{get_system_part_name(part_from)}')
            print(f"计划卖出：{market_code}  {price}  {num}")
            if market_code in self.buffer:
                old_num = self.buffer[market_code][0]
                if old_num == num:
                    self.buffer.pop(market_code)
                else:
                    self.buffer[market_code] = (
                        old_num - num, stoploss, goal_price)

        def get_asset_info(self):
            trader = get_xttrader(self._account, self._path)
            hku_check(trader is not None, "连接失败")

            asset = trader.query_stock_asset(self._account)
            total_asset = asset.total_asset if asset is not None else 0.0

            max_use_cash = 0.0
            if self.max_asset_percent > 0.0:
                max_use_cash = total_asset * self.max_asset_percent

            cash = asset.cash if asset is not None else 0.0
            if max_use_cash > 0 and cash > max_use_cash:
                cash = max_use_cash

            print("cash:", cash)

            positions = []
            xt_positions = trader.query_stock_positions(self._account)
            if xt_positions is not None:
                for xt_pos in xt_positions:
                    code, market = xt_pos.stock_code.split('.')
                    market_code = f"{code}.{market}"
                    if market_code in self.buffer:
                        stoploss, goal_price = self.buffer[market_code]
                    else:
                        stoploss, goal_price = 0.0, 0.0
                    positions.append(dict(market=market, code=code,
                                          number=xt_pos.can_use_volume, stoploss=stoploss, goal_price=goal_price, cost_price=xt_pos.avg_price))
            return dict(datetime=str(Datetime.now()), cash=cash, positions=positions)

    def part(account, path, stg_name='qmt', max_asset_percent=0.0):
        """创建 QMT 交易代理

        :param str account: QMT 交易账号
        :param str path: QMT 交易终端路径
        :parma stg_name: 交易策略名称
        :param float max_asset_percent: 最大可用资产比例，小于0时不限制，否则允许动用的现金不超过指定的总资产比例
        """
        return QmtTradeOrderBroker(account, path, stg_name, max_asset_percent)

except:
    from hikyuu import *

    def part(account, path, stg_name='qmt', max_asset_percent=0.0):
        hku_error("Could not found xtquant!")
        return None
