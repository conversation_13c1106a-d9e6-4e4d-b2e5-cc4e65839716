
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu.interactive import *
try:
    from .part import *
except:
    from part import *

import sys
if sys.platform == 'win32':
    import os
    os.system('chcp 65001')

if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    # if len(sys.argv) <= 1:
    #     print("ignore test")
    #     exit(0)

    if len(sys.argv) <= 1:
        ind = part('', r'D:\国金QMT交易端模拟\userdata_mini')
        asset = ind.get_asset_info()
        print(asset)
        ind.sell('SZ', '002455', 6.88, 100, 0.0, 0.0, SystemPart.SIGNAL)
        ind.buy('SZ', '002455', 6.88, 100, 0.0, 0.0, SystemPart.SIGNAL)

        import time
        time.sleep(60)
